import numpy as np
import pandas as pd
import torch
from torch.utils.data import Dataset, DataLoader
from mol_tree import Mo<PERSON><PERSON><PERSON>, update_revise_atoms, identify_revise_edges
import os, random, re
import pickle
from config import device
from functools import partial
from multiprocessing import Pool
from vocab import Vocab
from rdkit import Chem
import time
from chemutils import get_mol, get_smiles, set_atommap, get_synthon_from_smiles, canonicalize

class PairTreeFolder(object):

    def __init__(self, path, vocab, avocab, args, is_train_center=False):
        self.vocab = vocab
        self.avocab = avocab
        self.is_train_center = is_train_center
        
        self.batch_size = args.batch_size
        self.num_workers = args.ncpu
        self.shuffle = args.shuffle
        self.total_step = args.total_step
        self.total_epoch = args.epoch
        self.use_feature = args.use_feature
        # 移除分子树相关参数
        # self.use_brics = args.use_brics
        self.use_class = args.use_class
        self.use_atomic= args.use_atomic
        self.path = path
        self.epoch = 0
        self.step = 0
        
        if os.path.isfile(path):
            with open(path, 'rb') as f:
                self.mol_trees = pickle.load(f)
            self.files = [None]
        else:
            self.files = [f for f in os.listdir(path) if "tensor" in f and "pkl" in f]
            
    def __iter__(self):
        if not self.shuffle:
            batches_data = []
        
        unfinish = True
        while unfinish:
            for path in self.files:
                if path is None: mol_trees = self.mol_trees
                else:
                    mol_trees = None
                    with open(self.path + path, 'rb') as f:
                        mol_trees = pickle.load(f)
                        
                if self.shuffle or (not self.shuffle and i == 0):
                    batches = [mol_trees[j : j + self.batch_size] for j in range(0, len(mol_trees), self.batch_size)]
                    
                    if len(batches[-1]) < self.batch_size:
                        batches.pop()
                    
                    dataset = PairTreeDataset(batches, self.vocab, self.avocab, use_atomic=self.use_atomic, use_class=self.use_class,\
                                          use_feature=self.use_feature, is_train_center=self.is_train_center,
                                          use_contrastive=getattr(self, 'use_contrastive', False))
                                
                    dataloader = DataLoader(dataset, batch_size=1, shuffle=False,  num_workers=self.num_workers, collate_fn=lambda x:x[0])
                    
                    for b in dataloader:
                        if not self.shuffle:
                            batches_data.append(b)
                        
                        yield (b, self.epoch)
                        
                        self.step += 1
                        if self.step > self.total_step and self.total_step > 0:
                            unfinish = False
                            
                            break
                    
                    del batches, dataset, dataloader
                    
                    if self.shuffle and path is None:
                        random.shuffle(self.mol_trees)
                else:
                    for b in batches_data:
                        self.step += 1
                        yield (b, self.epoch)
                        if self.step > self.total_step:
                            unfinish = False
                            break
            if self.epoch > self.total_epoch and self.total_epoch > 0: unfinish = False
            self.epoch += 1
            if not unfinish: break
            
class MolTreeFolder(object):  
    def __init__(self, data, vocab, avocab, num_workers=10, batch_size=32, with_target=True, use_atomic=False, use_class=False, test=False, del_center=True, usepair=False, use_feature=True, shuffle=False):
        self.batch_size = batch_size
        self.vocab = vocab
        self.avocab = avocab
        self.shuffle = shuffle
        self.usepair = usepair
        # 移除分子树相关参数
        # self.use_brics = use_brics
        self.use_feature = use_feature
        self.test = test
        self.type_list = [data[i][0] for i in range(len(data))]
        self.idx_list = [data[i][1] for i in range(len(data))]
        self.del_center = del_center
        self.use_class = use_class
        self.use_atomic = use_atomic
        self.num_workers = num_workers
        self.with_target = with_target
        
        if self.test:
            self.reacts_list = [data[i][3] for i in range(len(data))]
            self.prod_list = [data[i][2] for i in range(len(data))]
        else:
            self.reacts_list = []
            for i in range(len(data)):
                reacts_smiles = data[i][3]
                self.reacts_list.append(reacts_smiles)
            self.prod_list = [data[i][2] for i in range(len(data))]
          
    def __iter__(self):
        batches = []
        
        for i in range(0, len(self.prod_list), self.batch_size):
            if self.use_class:
                batch = [(self.type_list[j], self.idx_list[j], self.prod_list[j], self.reacts_list[j]) for j in range(i, min(i + self.batch_size, len(self.prod_list)))]
            else:
                batch = [(None, self.idx_list[j], self.prod_list[j], self.reacts_list[j]) for j in range(i, min(i + self.batch_size, len(self.prod_list)))]
            
            batches.append(batch)
        
        dataset = MolTreeDataset(batches, self.vocab, self.avocab, with_target=self.with_target, use_atomic=self.use_atomic, use_class=self.use_class, test=self.test, del_center=self.del_center, usepair=self.usepair, use_feature=self.use_feature)
        
        dataloader = DataLoader(dataset, batch_size=1, num_workers=self.num_workers, shuffle=False, collate_fn=lambda x:x[0])
                
        for b in dataloader:
            yield b
        
        del batches, dataset, dataloader


class PairTreeDataset(Dataset):

    def __init__(self, data, vocab, avocab, use_class=False, use_atomic=False, is_train_center=False, use_feature=False, use_contrastive=False):
        self.data = data
        self.vocab = vocab
        self.avocab = avocab
        self.use_feature = use_feature
        # 移除分子树相关参数
        # self.use_brics = use_brics
        self.use_class = use_class
        self.use_atomic = use_atomic
        self.is_train_center = is_train_center
        self.use_contrastive = use_contrastive
        
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        batch_data = [tmp[0] for tmp in self.data[idx]]
        
        if self.is_train_center:

            if self.use_class:
                classes = [tmp[1] for i, tmp in enumerate(self.data[idx])]
            else:
                classes = None

            product_tree_batch = [dpair[0] for dpair in batch_data]
            idxs = [i for i, tree in enumerate(product_tree_batch) if len(tree.order) == 0 or '2H' in tree.smiles]
            if self.use_class: classes = [cls for i, cls in enumerate(classes) if i not in idxs]
            product_tree_batch, product_batch = MolTree.tensorize(product_tree_batch, self.vocab, self.avocab, skip_mols=idxs, use_atomic=self.use_atomic, use_feature=self.use_feature, product=True)

            # 如果启用对比学习，添加合成子数据
            if self.use_contrastive:
                try:
                    # 获取合成子分子树 (索引1)
                    synthon_tree_batch = [dpair[1] for dpair in batch_data]  # 合成子在索引1
                    # 过滤掉无效的合成子
                    synthon_tree_batch = [tree for i, tree in enumerate(synthon_tree_batch) if i not in idxs and tree is not None]

                    if synthon_tree_batch:
                        # 张量化合成子数据 - 使用product=False，因为合成子不是产物
                        synthon_tree_batch, synthon_batch = MolTree.tensorize(synthon_tree_batch, self.vocab, self.avocab,
                                                                             skip_mols=[], use_atomic=self.use_atomic,
                                                                             use_feature=self.use_feature, product=False, istest=True)
                        return classes, product_batch, product_tree_batch, synthon_batch, synthon_tree_batch
                    else:
                        # 如果没有有效的合成子数据，返回标准格式
                        return classes, product_batch, product_tree_batch
                except Exception as e:
                    print(f"合成子数据处理错误: {e}")
                    # 出错时返回标准格式
                    return classes, product_batch, product_tree_batch
            else:
                return classes, product_batch, product_tree_batch
        else:
            product_tree_batch = [dpair[0] for dpair in batch_data]
            synthon_tree_batch = [dpair[1] for dpair in batch_data]
            reacts_tree_batch  = [dpair[2] for dpair in batch_data]
            
            idxs = [i for i, tree in enumerate(product_tree_batch) if len(tree.order) == 0 or '2H' in reacts_tree_batch[i].smiles]
            
            for i, tree in enumerate(reacts_tree_batch):
                for _, idx in tree.order:
                    try:
                        if idx >= 0:
                            label = self.vocab[tree.mol_tree.nodes[idx]['label']]
                    except:
                        idxs.append(i)
                        break
            
            
            product_tree_batch, product_batch = MolTree.tensorize(product_tree_batch, self.vocab, self.avocab, skip_mols=idxs, use_atomic=self.use_atomic, istest=True, use_feature=self.use_feature, use_brics=False, product=True)
            
            reacts_tree_batch, reacts_batch = MolTree.tensorize(reacts_tree_batch, self.vocab, self.avocab, skip_mols=idxs, use_atomic=self.use_atomic, use_feature=self.use_feature, product=False)
            
            if self.use_class:
                classes = [tmp[1] for i, tmp in enumerate(self.data[idx]) if i not in idxs]
            else:
                classes = None
            
            return classes, product_batch, reacts_batch, product_tree_batch, reacts_tree_batch


class MolTreeDataset(Dataset):

    def __init__(self, data, vocab, avocab, with_target=True, use_class=False, del_center=True, use_atomic=False, test=False, usepair=False, use_feature=False):
        self.data = data
        self.vocab = vocab
        self.avocab = avocab
        self.usepair = usepair
        self.test = test
        self.use_class = use_class
        # 移除分子树相关参数
        # self.use_brics = use_brics
        self.use_feature = use_feature
        self.del_center = del_center
        self.use_atomic = use_atomic
        self.with_target = with_target
        
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        all_smiles = self.data[idx]
        product_trees, synthons_trees, react_smiles, target_idxs = [], [], [], []
        reaction_clses, product_smiles, synthon_smiles, skip_idxs = [], [], [], []

        # check whether the product SMILES strings include atom mapping number
        with_map_num = True
        if not self.with_target:
            with_map_num = max([atom.GetAtomMapNum() for atom in Chem.MolFromSmiles(all_smiles[0][2]).GetAtoms()]) > 0
        
        for i, (cls, idx, prod_smile, react_smile) in enumerate(all_smiles):
            product_smiles.append( (idx, prod_smile) )

            # add atom map number
            if not with_map_num:
                mol = Chem.MolFromSmiles(prod_smile)
                for atom in mol.GetAtoms():
                    atom.SetAtomMapNum(atom.GetIdx()+1)
                prod_smile = Chem.MolToSmiles(mol)
            
            react_smiles.append(react_smile)
            
            try:
                tree = MolTree(prod_smile)
            except Exception as e:
                print(e)
                skip_idxs.append(i)
                continue

            if self.with_target:
                react_mol = get_mol(react_smile)
                mol, synthon_smile = get_synthon_from_smiles(react_smile)
                synthon_smiles.append(synthon_smile)
            
                if '2H' in react_smiles:
                    skip_idxs.append(i)
                    continue

                if self.usepair or not self.del_center:
                    react_tree = MolTree(react_smile)
                    synthon_tree = MolTree(synthon_smile)
                
                    try:
                        update_revise_atoms(tree, react_tree)
                    except Exception as e:
                        print(e)
                        print("%s>>%s" % (react_smile, prod_smile))
                        skip_idxs.append(i)
                        continue
                     
                    synthons_trees.append(synthon_tree)

                reaction_clses.append(cls)

            
            product_trees.append(tree)
        
        
        _, product_batch = MolTree.tensorize(product_trees, self.vocab, self.avocab, istest=self.test, use_atomic=self.use_atomic, product=True, use_feature=self.use_feature)
        
        if self.with_target and self.del_center:
            for tree in product_trees:
                for node in tree.mol_graph.nodes:
                    if 'attach' in tree.mol_graph.nodes[node]:
                        del tree.mol_graph.nodes[node]['attach']
                 
                for idx1, idx2 in tree.mol_graph.edges:
                    if 'delete' in tree.mol_graph[idx1][idx2]:
                        del tree.mol_graph[idx1][idx2]['delete']
                    if 'change' in tree.mol_graph[idx1][idx2]:
                        del tree.mol_graph[idx1][idx2]['change']
        
        if self.use_class:
            select_clses = reaction_clses #[reaction_clses[i] for i in range(len(reaction_clses)) if i not in skip_idxs]
        else:
            select_clses = None
        
        # 为对比学习添加反应物张量化数据
        reactant_trees = []
        if self.with_target and hasattr(self, 'use_contrastive') and self.use_contrastive:
            # 构建反应物分子树
            for i, react_smile in enumerate(react_smiles):
                if i not in skip_idxs:
                    try:
                        react_tree = MolTree(react_smile)
                        reactant_trees.append(react_tree)
                    except:
                        # 如果反应物解析失败，使用产物树作为替代
                        reactant_trees.append(product_trees[len(reactant_trees)])

            # 张量化反应物数据
            if reactant_trees:
                _, reactant_batch = MolTree.tensorize(reactant_trees, self.vocab, self.avocab,
                                                    istest=self.test, use_atomic=self.use_atomic,
                                                    product=True, use_feature=self.use_feature)
            else:
                reactant_batch = None
        else:
            reactant_batch = None
            reactant_trees = []

        if self.usepair:
            synthons_trees, synthon_batch = MolTree.tensorize(synthons_trees, self.vocab, self.avocab, use_atomic=self.use_atomic, use_brics=False, product=False, istest=True, usemask=False, use_feature=self.use_feature)
            if reactant_batch is not None:
                return select_clses, product_batch, synthon_batch, product_trees, synthons_trees, reactant_batch, reactant_trees, react_smiles, product_smiles, synthon_smiles, skip_idxs
            else:
                return select_clses, product_batch, synthon_batch, product_trees, synthons_trees, react_smiles, product_smiles, synthon_smiles, skip_idxs
        else:
            if reactant_batch is not None:
                return select_clses, product_batch, product_trees, reactant_batch, reactant_trees, react_smiles, product_smiles, synthon_smiles, skip_idxs
            else:
                return select_clses, product_batch, product_trees, react_smiles, product_smiles, synthon_smiles, skip_idxs
