from enum import Enum

class ErrorCode(Enum):
    # 成功
    SUCCESS = 0
    
    # SMILES / 输入类错误 (100-199)
    INPUT_INVALID_SMILES = 101
    INPUT_INVALID_RESULTS_LIST = 102
    INPUT_INVALID_TOP_N = 103
    INPUT_INVALID_MAX_STEPS = 104
    INPUT_INVALID_BEAM_SIZE = 105
    INPUT_INVALID_RXN_CLASS = 106
    INPUT_MISSING_RXN_CLASS = 107
    
    # 模型加载类错误 (200-299)
    MODEL_FILE_NOT_FOUND = 201
    MODEL_LOAD_FAILED = 202
    
    # 预测类错误 (300-399)
    PREDICTION_NO_VALID_REACTANTS = 301
    PREDICTION_ALL_CLASSES_FAILED = 302
    PREDICTION_EXECUTION_ERROR = 303
    
    # 未知错误
    UNKNOWN_ERROR = 999


class RetroApiError(Exception):
    """逆合成预测API的异常类"""
    def __init__(self, message: str, status: Enum):
        self.message = message
        self.status = status
        super().__init__(f"[错误代码 {status.value}] {message}")
