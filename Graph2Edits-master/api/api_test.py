"""
RetroApi 测试程序 - 支持多反应物和更好的错误处理
"""
import sys
import os
from typing import List

# 导入API
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from RetroApi import PredictRetrosynthesis, ErrorCode
from api.ReactionData import Reactant, ReactionCondition, ReactionYield, ReactionData

def test_with_params(product_smiles, rxn_class=None, try_all_classes=True):
    """使用指定参数测试API并打印结果"""
    results: List[ReactionData] = []
    
    print(f"\n测试产物: {product_smiles}")
    print(f"参数: rxnClass={rxn_class}, tryAllClasses={try_all_classes}")
    
    # 调用逆合成预测函数
    returnCode = PredictRetrosynthesis(
        productSmiles=product_smiles,
        reactionResultsList=results,
        topNResults=5,
        rxnClass=rxn_class,
        tryAllClasses=try_all_classes,
        maxSteps=9,
        beamSize=10
    )
    
    # 检查返回结果    
    if returnCode == ErrorCode.SUCCESS:
        print(f"{returnCode.name}： {returnCode.value}")
        for i, result in enumerate(results):
            print(f"\n结果 {i+1}:")
            print(f"产物: {result.product}")
            print(f"反应物数量: {len(result.reactants)}")
            
            # 打印每个反应物的信息
            for j, reactant in enumerate(result.reactants):
                print(f"  反应物 {j+1}:")
                print(f"    SMILES: {reactant.smiles}")
                print(f"    反应类别: {reactant.rxnClass}")
                print(f"    预测概率: {reactant.probability:.4f}")
                print(f"    排名: {reactant.rank}")
    else:
        # returnCode已经是枚举对象，直接获取名称
        print(f"错误: {returnCode.name}，错误码：{returnCode.value}")
    
    print("="*50)
    return returnCode

def main():
    # 测试用例 - 咖啡因
    caffeine = "CN1C=NC2=C1C(=O)N(C(=O)N2C)C"
    
    # 测试用例 - 可能生成多个反应物的化合物
    benzyl_phenyl_ether = "c1ccccc1OCc2ccccc2"  # 苄基苯基醚
    biphenyl = "c1ccccc1-c2ccccc2"  # 联苯
    ethyl_benzoate = "CCOC(=O)c1ccccc1"  # 苯甲酸乙酯
    
    # 测试1: 正常情况 - 指定反应类别
    test_with_params(caffeine, rxn_class=7, try_all_classes=False)
    
    # 测试2: 正常情况 - 尝试所有反应类别
    test_with_params(biphenyl, rxn_class=None, try_all_classes=True)
    
    # 测试3: 错误情况 - 未指定反应类别且不尝试所有类别
    test_with_params(benzyl_phenyl_ether, rxn_class=None, try_all_classes=False)
    
    # 测试4: 苯甲酸乙酯 - 应该产生多个反应物（酸和醇）
    test_with_params(ethyl_benzoate, rxn_class=None, try_all_classes=True)

if __name__ == "__main__":
    main()