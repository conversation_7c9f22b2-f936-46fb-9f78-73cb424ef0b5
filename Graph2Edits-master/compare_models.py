# compare_models.py
import argparse
import os
import subprocess
import pandas as pd

def run_evaluation(dataset, exp_name, use_contrastive=False, use_rxn_class=False):
    """运行评估并返回结果"""
    cmd = [
        'python', 'eval_contrastive.py',
        '--dataset', dataset,
        '--experiments', exp_name,
        '--beam_size', '10'
    ]
    
    if use_contrastive:
        cmd.append('--use_contrastive')
    if use_rxn_class:
        cmd.append('--use_rxn_class')
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        return result.stdout, result.stderr
    except Exception as e:
        return None, str(e)

def parse_results(output_text):
    """解析评估输出结果"""
    lines = output_text.split('\n')
    results = {}
    
    for line in lines:
        if 'Top-' in line and 'Accuracy:' in line:
            parts = line.split(':')
            if len(parts) == 2:
                key = parts[0].strip()
                value = float(parts[1].strip())
                results[key] = value
    
    return results

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--dataset', type=str, default='uspto_50k')
    parser.add_argument('--baseline_exp', type=str, required=True,
                        help='Baseline experiment name')
    parser.add_argument('--contrastive_exp', type=str, required=True,
                        help='Contrastive experiment name')
    parser.add_argument('--use_rxn_class', action='store_true')
    
    args = parser.parse_args()
    
    print("Comparing Baseline vs Contrastive Learning Models")
    print("="*60)
    
    # 运行基线模型评估
    print("Evaluating baseline model...")
    baseline_output, baseline_error = run_evaluation(
        args.dataset, args.baseline_exp, 
        use_contrastive=False, use_rxn_class=args.use_rxn_class
    )
    
    # 运行对比学习模型评估
    print("Evaluating contrastive learning model...")
    contrastive_output, contrastive_error = run_evaluation(
        args.dataset, args.contrastive_exp,
        use_contrastive=True, use_rxn_class=args.use_rxn_class
    )
    
    if baseline_output and contrastive_output:
        baseline_results = parse_results(baseline_output)
        contrastive_results = parse_results(contrastive_output)
        
        # 创建对比表格
        comparison_data = []
        for metric in ['Top-1 Accuracy', 'Top-3 Accuracy', 'Top-5 Accuracy', 'Top-10 Accuracy']:
            baseline_val = baseline_results.get(metric, 0)
            contrastive_val = contrastive_results.get(metric, 0)
            improvement = contrastive_val - baseline_val
            improvement_pct = (improvement / baseline_val * 100) if baseline_val > 0 else 0
            
            comparison_data.append({
                'Metric': metric,
                'Baseline': f'{baseline_val:.4f}',
                'Contrastive': f'{contrastive_val:.4f}',
                'Improvement': f'{improvement:+.4f}',
                'Improvement (%)': f'{improvement_pct:+.2f}%'
            })
        
        df = pd.DataFrame(comparison_data)
        print("\nComparison Results:")
        print(df.to_string(index=False))
        
        # 保存结果
        output_file = f'comparison_{args.dataset}_{"rxn_class" if args.use_rxn_class else "no_rxn_class"}.csv'
        df.to_csv(output_file, index=False)
        print(f"\nResults saved to: {output_file}")
        
    else:
        print("Error in evaluation:")
        if baseline_error:
            print(f"Baseline error: {baseline_error}")
        if contrastive_error:
            print(f"Contrastive error: {contrastive_error}")

if __name__ == '__main__':
    main()