# eval_contrastive.py
import numpy as np
import pandas as pd
import os
import argparse
import joblib
from tqdm import tqdm
from collections import Counter
import torch
from rdkit import Chem, RDLogger

from models.graph2edits_contrastive import Graph2EditsWithContrastive
from models.graph2edits import Graph2Edits
from models.beam_search import BeamSearch
lg = RDLogger.logger()
lg.setLevel(4)

ROOT_DIR = './'
DEVICE = "cuda" if torch.cuda.is_available() else "cpu"

def canonicalize(smi):
    try:
        mol = Chem.MolFromSmiles(smi)
    except:
        print('no mol', flush=True)
        return smi
    if mol is None:
        return smi
    mol = Chem.RemoveHs(mol)
    [a.ClearProp('molAtomMapNumber') for a in mol.GetAtoms()]
    return Chem.MolToSmiles(mol)

def load_model(checkpoint_path, use_contrastive=False):
    """加载模型（支持对比学习版本）"""
    checkpoint = torch.load(checkpoint_path, map_location=DEVICE)
    config = checkpoint['saveables']
    
    if use_contrastive:
        model = Graph2EditsWithContrastive(**config, device=DEVICE)
    else:
        model = Graph2Edits(**config, device=DEVICE)
    
    model.load_state_dict(checkpoint['state'])
    model.to(DEVICE)
    model.eval()
    return model

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--dataset', type=str, default='uspto_50k',
                        help='dataset: uspto_50k or uspto_full')
    parser.add_argument("--use_rxn_class", default=False,
                        action='store_true', help='Whether to use rxn_class')
    parser.add_argument("--use_contrastive", default=False,
                        action='store_true', help='Whether model uses contrastive learning')
    parser.add_argument('--experiments', type=str, default='27-06-2022--10-27-22',
                        help='Name of edits prediction experiment')
    parser.add_argument('--beam_size', type=int, default=10, help='Beam search width')
    parser.add_argument('--max_steps', type=int, default=9,
                        help='maximum number of edit steps')

    args = parser.parse_args()
    args.dataset = args.dataset.lower()

    # 加载测试数据
    data_dir = os.path.join(ROOT_DIR, 'data', f'{args.dataset}', 'test')
    test_file = os.path.join(data_dir, 'test.file.kekulized')
    test_data = joblib.load(test_file)
    
    # 确定实验目录
    if args.use_contrastive:
        exp_type = 'with_contrastive'
    else:
        exp_type = 'without_contrastive'
        
    if args.use_rxn_class:
        exp_dir = os.path.join(ROOT_DIR, 'experiments', f'{args.dataset}', 
                               'with_rxn_class', exp_type, f'{args.experiments}')
    else:
        exp_dir = os.path.join(ROOT_DIR, 'experiments', f'{args.dataset}', 
                               'without_rxn_class', exp_type, f'{args.experiments}')

    # 查找最新的检查点
    checkpoint_files = [f for f in os.listdir(exp_dir) if f.startswith('epoch_') and f.endswith('.pt')]
    if not checkpoint_files:
        print(f"No checkpoint files found in {exp_dir}")
        return
    
    # 选择最高epoch的检查点
    epochs = [int(f.split('_')[1].split('.')[0]) for f in checkpoint_files]
    latest_epoch = max(epochs)
    checkpoint_path = os.path.join(exp_dir, f'epoch_{latest_epoch}.pt')
    
    print(f"Loading model from: {checkpoint_path}")
    model = load_model(checkpoint_path, use_contrastive=args.use_contrastive)

    # 初始化BeamSearch
    beam_model = BeamSearch(model=model, step_beam_size=10,
                            beam_size=args.beam_size, use_rxn_class=args.use_rxn_class)
    
    # 评估指标
    top_k = np.zeros(args.beam_size)
    edit_steps_cor = []
    counter = []
    stereo_rxn = []
    stereo_rxn_cor = []
    
    # 创建结果文件
    pred_file = os.path.join(exp_dir, 'pred_results_contrastive.txt' if args.use_contrastive else 'pred_results_baseline.txt')
    file_num = 1
    while os.path.exists(pred_file):
        pred_file = os.path.join(exp_dir, f'pred_results_{"contrastive" if args.use_contrastive else "baseline"}_{file_num}.txt')
        file_num += 1

    p_bar = tqdm(list(range(len(test_data))))

    with open(pred_file, 'w') as fp:
        fp.write(f"Evaluation Results - {'With Contrastive Learning' if args.use_contrastive else 'Baseline Model'}\n")
        fp.write(f"Model: {checkpoint_path}\n")
        fp.write(f"Beam Size: {args.beam_size}\n")
        fp.write(f"Max Steps: {args.max_steps}\n")
        fp.write("="*80 + "\n\n")
        
        for idx in p_bar:
            rxn_data = test_data[idx]
            rxn_smi = rxn_data.rxn_smi
            rxn_class = rxn_data.rxn_class
            edit_steps = len(rxn_data.edits)
            counter.append(edit_steps)

            r, p = rxn_smi.split('>>')
            r_mol = Chem.MolFromSmiles(r)
            [a.ClearProp('molAtomMapNumber') for a in r_mol.GetAtoms()]
            r_mol = Chem.MolFromSmiles(Chem.MolToSmiles(r_mol))
            r_smi = Chem.MolToSmiles(r_mol, isomericSmiles=True)
            r_set = set(r_smi.split('.'))

            with torch.no_grad():
                top_k_results = beam_model.run_search(
                    prod_smi=p, max_steps=args.max_steps, rxn_class=rxn_class)

            fp.write(f'({idx}) {rxn_smi}\n')
            fp.write(f'True edit steps: {edit_steps}\n')

            beam_matched = False
            for beam_idx, path in enumerate(top_k_results):
                pred_smi = path['final_smi']
                prob = path['prob']
                pred_set = set(pred_smi.split('.'))
                correct = pred_set == r_set
                
                # 构建编辑信息字符串
                str_edits = '|'.join(f'({str(edit)};{p:.4f})' for edit, p in zip(
                    path['rxn_actions'], path['edits_prob']))
                
                fp.write(f'  Rank {beam_idx+1}: correct={correct}, prob={prob:.4f}\n')
                fp.write(f'    Predicted: {pred_smi}\n')
                fp.write(f'    Edits: {str_edits}\n')
                
                if correct and not beam_matched:
                    top_k[beam_idx] += 1
                    beam_matched = True

            fp.write('\n')
            
            if beam_matched:
                edit_steps_cor.append(edit_steps)

            # 检查立体化学反应
            for edit in rxn_data.edits:
                if hasattr(edit, '__len__') and len(edit) > 1:
                    if edit[1] in [(1, 1), (1, 2), (0, 1), (0, 2), (2, 2), (2, 3)]:
                        stereo_rxn.append(idx)
                        if beam_matched:
                            stereo_rxn_cor.append(idx)
                        break

            # 更新进度条
            msg = 'Average accuracy'
            for beam_idx in [1, 3, 5, 10]:
                if beam_idx <= args.beam_size:
                    match_acc = np.sum(top_k[:beam_idx]) / (idx + 1)
                    msg += f', t{beam_idx}: {match_acc:.4f}'
            p_bar.set_description(msg)

        # 写入统计信息
        edit_steps_stats = Counter(counter)
        edit_steps_correct_stats = Counter(edit_steps_cor)
        
        fp.write("\n" + "="*80 + "\n")
        fp.write("FINAL STATISTICS\n")
        fp.write("="*80 + "\n")
        
        # 准确率统计
        fp.write("Top-k Accuracy:\n")
        for k in [1, 3, 5, 10]:
            if k <= args.beam_size:
                acc = np.sum(top_k[:k]) / len(test_data)
                fp.write(f"  Top-{k}: {acc:.4f} ({np.sum(top_k[:k]):.0f}/{len(test_data)})\n")
        
        fp.write(f"\nEdit Steps Distribution:\n")
        for steps in sorted(edit_steps_stats.keys()):
            count = edit_steps_stats[steps]
            correct = edit_steps_correct_stats.get(steps, 0)
            acc = correct / count if count > 0 else 0
            fp.write(f"  {steps} steps: {correct}/{count} = {acc:.4f}\n")
        
        fp.write(f"\nStereochemistry Results:\n")
        fp.write(f"  Stereo reactions: {len(stereo_rxn)}\n")
        fp.write(f"  Stereo correct: {len(stereo_rxn_cor)}\n")
        if len(stereo_rxn) > 0:
            stereo_acc = len(stereo_rxn_cor) / len(stereo_rxn)
            fp.write(f"  Stereo accuracy: {stereo_acc:.4f}\n")

    print("\nFinal Results:")
    print("="*50)
    for k in [1, 3, 5, 10]:
        if k <= args.beam_size:
            acc = np.sum(top_k[:k]) / len(test_data)
            print(f"Top-{k} Accuracy: {acc:.4f}")

if __name__ == '__main__':
    main()