# models/graph2edits_contrastive.py
from typing import Dict, List, Tuple, Union
import torch
import torch.nn as nn
import torch.nn.functional as F
from models.graph2edits import Graph2Edits

class Graph2EditsWithContrastive(Graph2Edits):
    
    def __init__(self, config: Dict, atom_vocab, bond_vocab, device: str = 'cpu',
                 contrastive_weight: float = 0.1, temperature: float = 0.1,
                 projection_dim: int = 256):
        super().__init__(config, atom_vocab, bond_vocab, device)
        
        self.contrastive_weight = contrastive_weight
        self.temperature = temperature
        
        # 对比学习投影头
        self.projection_head = nn.Sequential(
            nn.Linear(config['mpn_size'], config['mpn_size'] // 2),
            nn.ReLU(),
            nn.Dropout(p=config.get('dropout_mlp', 0.2)),
            nn.Linear(config['mpn_size'] // 2, projection_dim)
        )
    
    def get_molecule_representation(self, prod_tensors, prod_scopes):
        """获取分子级别的表示"""
        prod_tensors = self.to_device(prod_tensors)
        atom_scope, bond_scope = prod_scopes
        
        # 使用现有的编码器获取原子表示
        a_feats = self.encoder(prod_tensors, mask=None)
        
        # 应用注意力机制（如果启用）
        if self.config.get('use_attn', False):
            from models.model_utils import creat_edits_feats, unbatch_feats
            feats, mask = creat_edits_feats(a_feats, atom_scope)
            attention_score, feats = self.attn(feats, mask)
            a_feats = unbatch_feats(feats, atom_scope)
        
        # 聚合为图级表示（平均池化）
        graph_reprs = torch.stack([
            a_feats[st: st + le].mean(dim=0) 
            for st, le in atom_scope
        ])
        
        return graph_reprs
    
    def forward_contrastive(self, prod_tensors, prod_scopes):
        """对比学习前向传播"""
        graph_reprs = self.get_molecule_representation(prod_tensors, prod_scopes)
        projections = self.projection_head(graph_reprs)
        projections = F.normalize(projections, dim=1)
        return projections
    
    def create_feature_augmented_views(self, prod_tensors, prod_scopes):
        """创建特征级增强视图 - 避免复杂的分子图修改"""
        try:
            f_atoms, f_bonds, a2b, b2a, b2revb, undirected_b2a = prod_tensors
            
            # 使用不同的dropout率创建两个增强视图
            dropout_rate_1 = 0.1
            dropout_rate_2 = 0.15
            
            # 增强视图1：轻微的特征dropout
            f_atoms_1 = f_atoms.clone()
            f_bonds_1 = f_bonds.clone()
            if self.training:
                # 原子特征dropout
                atom_mask = torch.rand_like(f_atoms_1) > dropout_rate_1
                f_atoms_1 = f_atoms_1 * atom_mask.float()
                # 键特征dropout
                bond_mask = torch.rand_like(f_bonds_1) > dropout_rate_1
                f_bonds_1 = f_bonds_1 * bond_mask.float()
            
            # 增强视图2：稍强的特征dropout
            f_atoms_2 = f_atoms.clone()
            f_bonds_2 = f_bonds.clone()
            if self.training:
                # 原子特征dropout
                atom_mask2 = torch.rand_like(f_atoms_2) > dropout_rate_2
                f_atoms_2 = f_atoms_2 * atom_mask2.float()
                # 键特征dropout
                bond_mask2 = torch.rand_like(f_bonds_2) > dropout_rate_2
                f_bonds_2 = f_bonds_2 * bond_mask2.float()
            
            # 保持图结构不变，只修改特征
            aug1_tensors = (f_atoms_1, f_bonds_1, a2b, b2a, b2revb, undirected_b2a)
            aug2_tensors = (f_atoms_2, f_bonds_2, a2b, b2a, b2revb, undirected_b2a)
            
            return aug1_tensors, prod_scopes, aug2_tensors, prod_scopes
            
        except Exception as e:
            # 如果增强失败，返回None
            return None, None, None, None
    
    def compute_contrastive_loss(self, prod_tensors, prod_scopes):
        """计算对比学习损失"""
        try:
            # 创建特征级增强视图
            aug1_tensors, aug1_scopes, aug2_tensors, aug2_scopes = self.create_feature_augmented_views(
                prod_tensors, prod_scopes)
            
            if aug1_tensors is None:
                return torch.tensor(0.0, device=self.device)
            
            # 获取对比表示
            z1 = self.forward_contrastive(aug1_tensors, aug1_scopes)
            z2 = self.forward_contrastive(aug2_tensors, aug2_scopes)
            
            # 确保有足够的样本进行对比学习
            if z1.size(0) < 2:
                return torch.tensor(0.0, device=self.device)
            
            # 计算NT-Xent损失
            loss = self.nt_xent_loss(z1, z2)
            return loss
            
        except Exception as e:
            # 静默处理错误，返回零损失
            return torch.tensor(0.0, device=self.device)
    
    def nt_xent_loss(self, z1, z2):
        """计算NT-Xent损失"""
        batch_size = z1.size(0)
        if batch_size < 2:
            return torch.tensor(0.0, device=self.device)
        
        # 拼接表示
        z = torch.cat([z1, z2], dim=0)  # [2*batch_size, dim]
        
        # 计算相似性矩阵
        sim_matrix = torch.mm(z, z.t()) / self.temperature
        
        # 创建标签
        labels = torch.cat([
            torch.arange(batch_size, 2*batch_size),
            torch.arange(0, batch_size)
        ]).to(self.device)
        
        # 移除对角线
        mask = torch.eye(2*batch_size, dtype=torch.bool).to(self.device)
        sim_matrix = sim_matrix.masked_fill(mask, -float('inf'))
        
        # 计算损失
        loss = F.cross_entropy(sim_matrix, labels)
        return loss
    
    def forward(self, prod_seq_inputs: List[Tuple[torch.Tensor, List]]) -> Tuple[torch.Tensor, torch.Tensor]:
        """扩展的前向传播，同时返回编辑预测和对比学习损失"""
        # 原始的编辑预测
        seq_edit_scores = super().forward(prod_seq_inputs)
        
        # 计算对比学习损失（仅在训练时）
        contrastive_loss = torch.tensor(0.0, device=self.device)
        
        if self.training and len(prod_seq_inputs) > 0:
            # 对序列中的每一步计算对比学习损失
            total_contrastive_loss = 0
            valid_steps = 0
            
            for prod_tensors, prod_scopes in prod_seq_inputs:
                step_loss = self.compute_contrastive_loss(prod_tensors, prod_scopes)
                if step_loss.item() > 0:
                    total_contrastive_loss += step_loss
                    valid_steps += 1
            
            if valid_steps > 0:
                contrastive_loss = total_contrastive_loss / valid_steps
        
        return seq_edit_scores, contrastive_loss
    
    def get_saveables(self) -> Dict:
        """返回模型保存所需的属性"""
        saveables = super().get_saveables()
        saveables.update({
            'contrastive_weight': self.contrastive_weight,
            'temperature': self.temperature
        })
        return saveables
from typing import Dict, List, Tuple, Union
import torch
import torch.nn as nn
import torch.nn.functional as F
from models.graph2edits import Graph2Edits

class Graph2EditsWithContrastive(Graph2Edits):
    """修复后的对比学习Graph2Edits模型 - 基于调试版本的成功策略"""
    
    def __init__(self, config: Dict, atom_vocab, bond_vocab, device: str = 'cpu',
                 contrastive_weight: float = 0.1, temperature: float = 0.1,
                 projection_dim: int = 256):
        super().__init__(config, atom_vocab, bond_vocab, device)
        
        self.contrastive_weight = contrastive_weight
        self.temperature = temperature
        
        # 对比学习投影头
        self.projection_head = nn.Sequential(
            nn.Linear(config['mpn_size'], config['mpn_size'] // 2),
            nn.ReLU(),
            nn.Dropout(p=config.get('dropout_mlp', 0.2)),
            nn.Linear(config['mpn_size'] // 2, projection_dim)
        )
    
    def get_molecule_representation(self, prod_tensors, prod_scopes):
        """获取分子级别的表示"""
        prod_tensors = self.to_device(prod_tensors)
        atom_scope, bond_scope = prod_scopes
        
        # 使用现有的编码器获取原子表示
        a_feats = self.encoder(prod_tensors, mask=None)
        
        # 应用注意力机制（如果启用）
        if self.config.get('use_attn', False):
            from models.model_utils import creat_edits_feats, unbatch_feats
            feats, mask = creat_edits_feats(a_feats, atom_scope)
            attention_score, feats = self.attn(feats, mask)
            a_feats = unbatch_feats(feats, atom_scope)
        
        # 聚合为图级表示（平均池化）
        graph_reprs = torch.stack([
            a_feats[st: st + le].mean(dim=0) 
            for st, le in atom_scope
        ])
        
        return graph_reprs
    
    def forward_contrastive(self, prod_tensors, prod_scopes):
        """对比学习前向传播"""
        graph_reprs = self.get_molecule_representation(prod_tensors, prod_scopes)
        projections = self.projection_head(graph_reprs)
        projections = F.normalize(projections, dim=1)
        return projections
    
    def create_feature_augmented_views(self, prod_tensors, prod_scopes):
        """创建特征级增强视图 - 避免复杂的分子图修改"""
        try:
            f_atoms, f_bonds, a2b, b2a, b2revb, undirected_b2a = prod_tensors
            
            # 使用不同的dropout率创建两个增强视图
            dropout_rate_1 = 0.1
            dropout_rate_2 = 0.15
            
            # 增强视图1：轻微的特征dropout
            f_atoms_1 = f_atoms.clone()
            f_bonds_1 = f_bonds.clone()
            if self.training:
                # 原子特征dropout
                atom_mask = torch.rand_like(f_atoms_1) > dropout_rate_1
                f_atoms_1 = f_atoms_1 * atom_mask.float()
                # 键特征dropout
                bond_mask = torch.rand_like(f_bonds_1) > dropout_rate_1
                f_bonds_1 = f_bonds_1 * bond_mask.float()
            
            # 增强视图2：稍强的特征dropout
            f_atoms_2 = f_atoms.clone()
            f_bonds_2 = f_bonds.clone()
            if self.training:
                # 原子特征dropout
                atom_mask2 = torch.rand_like(f_atoms_2) > dropout_rate_2
                f_atoms_2 = f_atoms_2 * atom_mask2.float()
                # 键特征dropout
                bond_mask2 = torch.rand_like(f_bonds_2) > dropout_rate_2
                f_bonds_2 = f_bonds_2 * bond_mask2.float()
            
            # 保持图结构不变，只修改特征
            aug1_tensors = (f_atoms_1, f_bonds_1, a2b, b2a, b2revb, undirected_b2a)
            aug2_tensors = (f_atoms_2, f_bonds_2, a2b, b2a, b2revb, undirected_b2a)
            
            return aug1_tensors, prod_scopes, aug2_tensors, prod_scopes
            
        except Exception as e:
            # 如果增强失败，返回None
            return None, None, None, None
    
    def compute_contrastive_loss(self, prod_tensors, prod_scopes):
        """计算对比学习损失"""
        try:
            # 创建特征级增强视图
            aug1_tensors, aug1_scopes, aug2_tensors, aug2_scopes = self.create_feature_augmented_views(
                prod_tensors, prod_scopes)
            
            if aug1_tensors is None:
                return torch.tensor(0.0, device=self.device)
            
            # 获取对比表示
            z1 = self.forward_contrastive(aug1_tensors, aug1_scopes)
            z2 = self.forward_contrastive(aug2_tensors, aug2_scopes)
            
            # 确保有足够的样本进行对比学习
            if z1.size(0) < 2:
                return torch.tensor(0.0, device=self.device)
            
            # 计算NT-Xent损失
            loss = self.nt_xent_loss(z1, z2)
            return loss
            
        except Exception as e:
            # 静默处理错误，返回零损失
            return torch.tensor(0.0, device=self.device)
    
    def nt_xent_loss(self, z1, z2):
        """计算NT-Xent损失"""
        batch_size = z1.size(0)
        if batch_size < 2:
            return torch.tensor(0.0, device=self.device)
        
        # 拼接表示
        z = torch.cat([z1, z2], dim=0)  # [2*batch_size, dim]
        
        # 计算相似性矩阵
        sim_matrix = torch.mm(z, z.t()) / self.temperature
        
        # 创建标签
        labels = torch.cat([
            torch.arange(batch_size, 2*batch_size),
            torch.arange(0, batch_size)
        ]).to(self.device)
        
        # 移除对角线
        mask = torch.eye(2*batch_size, dtype=torch.bool).to(self.device)
        sim_matrix = sim_matrix.masked_fill(mask, -float('inf'))
        
        # 计算损失
        loss = F.cross_entropy(sim_matrix, labels)
        return loss
    
    def forward(self, prod_seq_inputs: List[Tuple[torch.Tensor, List]]) -> Tuple[torch.Tensor, torch.Tensor]:
        """扩展的前向传播，同时返回编辑预测和对比学习损失"""
        # 原始的编辑预测
        seq_edit_scores = super().forward(prod_seq_inputs)
        
        # 计算对比学习损失（仅在训练时）
        contrastive_loss = torch.tensor(0.0, device=self.device)
        
        if self.training and len(prod_seq_inputs) > 0:
            # 对序列中的每一步计算对比学习损失
            total_contrastive_loss = 0
            valid_steps = 0
            
            for prod_tensors, prod_scopes in prod_seq_inputs:
                step_loss = self.compute_contrastive_loss(prod_tensors, prod_scopes)
                if step_loss.item() > 0:
                    total_contrastive_loss += step_loss
                    valid_steps += 1
            
            if valid_steps > 0:
                contrastive_loss = total_contrastive_loss / valid_steps
        
        return seq_edit_scores, contrastive_loss
    
    def get_saveables(self) -> Dict:
        """返回模型保存所需的属性"""
        saveables = super().get_saveables()
        saveables.update({
            'contrastive_weight': self.contrastive_weight,
            'temperature': self.temperature
        })
        return saveables