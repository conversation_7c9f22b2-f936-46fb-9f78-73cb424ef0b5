# models/graph2edits_contrastive_debug.py
from typing import Dict, List, Tuple, Union
import torch
import torch.nn as nn
import torch.nn.functional as F
from models.graph2edits import Graph2Edits
from utils.molecular_augmentations import MolecularAugmentations
from utils.collate_fn import get_batch_graphs

class Graph2EditsWithContrastiveDebug(Graph2Edits):
    """调试版对比学习Graph2Edits模型"""
    
    def __init__(self, config: Dict, atom_vocab, bond_vocab, device: str = 'cpu',
                 contrastive_weight: float = 0.1, temperature: float = 0.1,
                 projection_dim: int = 256):
        super().__init__(config, atom_vocab, bond_vocab, device)
        
        self.contrastive_weight = contrastive_weight
        self.temperature = temperature
        
        # 对比学习投影头
        self.projection_head = nn.Sequential(
            nn.Linear(config['mpn_size'], config['mpn_size'] // 2),
            nn.ReLU(),
            nn.Dropout(p=config.get('dropout_mlp', 0.2)),
            nn.Linear(config['mpn_size'] // 2, projection_dim)
        )
        
        # 分子图增强器
        self.augmentations = MolecularAugmentations()
        
        # 调试计数器
        self.debug_counters = {
            'total_calls': 0,
            'successful_augmentations': 0,
            'failed_augmentations': 0,
            'valid_batches': 0,
            'contrastive_computations': 0
        }
    
    def get_molecule_representation(self, prod_tensors, prod_scopes):
        """获取分子级别的表示"""
        prod_tensors = self.to_device(prod_tensors)
        atom_scope, bond_scope = prod_scopes
        
        # 使用现有的编码器获取原子表示
        a_feats = self.encoder(prod_tensors, mask=None)
        
        # 应用注意力机制（如果启用）
        if self.config.get('use_attn', False):
            from models.model_utils import creat_edits_feats, unbatch_feats
            feats, mask = creat_edits_feats(a_feats, atom_scope)
            attention_score, feats = self.attn(feats, mask)
            a_feats = unbatch_feats(feats, atom_scope)
        
        # 聚合为图级表示
        graph_reprs = torch.stack([
            a_feats[st: st + le].mean(dim=0) 
            for st, le in atom_scope
        ])
        
        return graph_reprs
    
    def forward_contrastive(self, prod_tensors, prod_scopes):
        """对比学习前向传播"""
        graph_reprs = self.get_molecule_representation(prod_tensors, prod_scopes)
        projections = self.projection_head(graph_reprs)
        projections = F.normalize(projections, dim=1)
        return projections
    
    def create_simple_augmented_views(self, prod_tensors, prod_scopes):
        """创建简单的增强视图 - 用于调试"""
        try:
            # 简单的dropout增强，不改变图结构
            f_atoms, f_bonds, a2b, b2a, b2revb, undirected_b2a = prod_tensors
            
            # 创建两个不同的dropout版本
            dropout_rate_1 = 0.1
            dropout_rate_2 = 0.15
            
            # 增强视图1：轻微dropout
            f_atoms_1 = f_atoms.clone()
            f_bonds_1 = f_bonds.clone()
            if self.training:
                mask1 = torch.rand_like(f_atoms_1) > dropout_rate_1
                f_atoms_1 = f_atoms_1 * mask1.float()
                mask2 = torch.rand_like(f_bonds_1) > dropout_rate_1
                f_bonds_1 = f_bonds_1 * mask2.float()
            
            # 增强视图2：稍微更强的dropout
            f_atoms_2 = f_atoms.clone()
            f_bonds_2 = f_bonds.clone()
            if self.training:
                mask3 = torch.rand_like(f_atoms_2) > dropout_rate_2
                f_atoms_2 = f_atoms_2 * mask3.float()
                mask4 = torch.rand_like(f_bonds_2) > dropout_rate_2
                f_bonds_2 = f_bonds_2 * mask4.float()
            
            aug1_tensors = (f_atoms_1, f_bonds_1, a2b, b2a, b2revb, undirected_b2a)
            aug2_tensors = (f_atoms_2, f_bonds_2, a2b, b2a, b2revb, undirected_b2a)
            
            return aug1_tensors, prod_scopes, aug2_tensors, prod_scopes
        except Exception as e:
            print(f"简单增强失败: {e}")
            return None, None, None, None
    
    def compute_contrastive_loss(self, prod_tensors, prod_scopes):
        """计算对比学习损失 - 调试版本"""
        self.debug_counters['total_calls'] += 1
        
        try:
            # 使用简单的特征增强而不是复杂的分子图修改
            aug1_tensors, aug1_scopes, aug2_tensors, aug2_scopes = self.create_simple_augmented_views(
                prod_tensors, prod_scopes)
            
            if aug1_tensors is None:
                self.debug_counters['failed_augmentations'] += 1
                return torch.tensor(0.0, device=self.device)
            
            self.debug_counters['successful_augmentations'] += 1
            
            # 获取对比表示
            z1 = self.forward_contrastive(aug1_tensors, aug1_scopes)
            z2 = self.forward_contrastive(aug2_tensors, aug2_scopes)
            
            # 确保有足够的样本进行对比学习
            if z1.size(0) < 2:
                return torch.tensor(0.0, device=self.device)
            
            self.debug_counters['valid_batches'] += 1
            
            # 计算NT-Xent损失
            loss = self.nt_xent_loss(z1, z2)
            
            if loss.item() > 0:
                self.debug_counters['contrastive_computations'] += 1
            
            # 每100次调用打印一次调试信息
            if self.debug_counters['total_calls'] % 100 == 0:
                print(f"\n[DEBUG] 对比学习调试信息:")
                print(f"  总调用次数: {self.debug_counters['total_calls']}")
                print(f"  成功增强: {self.debug_counters['successful_augmentations']}")
                print(f"  失败增强: {self.debug_counters['failed_augmentations']}")
                print(f"  有效批次: {self.debug_counters['valid_batches']}")
                print(f"  对比计算: {self.debug_counters['contrastive_computations']}")
                print(f"  当前损失: {loss.item():.6f}")
                print(f"  z1 shape: {z1.shape}, z2 shape: {z2.shape}")
            
            return loss
            
        except Exception as e:
            self.debug_counters['failed_augmentations'] += 1
            print(f"[DEBUG] 对比学习计算失败: {e}")
            return torch.tensor(0.0, device=self.device)
    
    def nt_xent_loss(self, z1, z2):
        """计算NT-Xent损失"""
        batch_size = z1.size(0)
        if batch_size < 2:
            return torch.tensor(0.0, device=self.device)
        
        # 拼接表示
        z = torch.cat([z1, z2], dim=0)  # [2*batch_size, dim]
        
        # 计算相似性矩阵
        sim_matrix = torch.mm(z, z.t()) / self.temperature
        
        # 创建标签
        labels = torch.cat([
            torch.arange(batch_size, 2*batch_size),
            torch.arange(0, batch_size)
        ]).to(self.device)
        
        # 移除对角线
        mask = torch.eye(2*batch_size, dtype=torch.bool).to(self.device)
        sim_matrix = sim_matrix.masked_fill(mask, -float('inf'))
        
        # 计算损失
        loss = F.cross_entropy(sim_matrix, labels)
        return loss
    
    def forward(self, prod_seq_inputs: List[Tuple[torch.Tensor, List]]) -> Tuple[torch.Tensor, torch.Tensor]:
        """扩展的前向传播"""
        # 原始的编辑预测
        seq_edit_scores = super().forward(prod_seq_inputs)
        
        # 计算对比学习损失（仅在训练时）
        contrastive_loss = torch.tensor(0.0, device=self.device)
        
        if self.training and len(prod_seq_inputs) > 0:
            # 对序列中的每一步计算对比学习损失
            total_contrastive_loss = 0
            valid_steps = 0
            
            for prod_tensors, prod_scopes in prod_seq_inputs:
                step_loss = self.compute_contrastive_loss(prod_tensors, prod_scopes)
                if step_loss.item() > 0:
                    total_contrastive_loss += step_loss
                    valid_steps += 1
            
            if valid_steps > 0:
                contrastive_loss = total_contrastive_loss / valid_steps
        
        return seq_edit_scores, contrastive_loss