# train_contrastive_debug.py
import argparse
import os
import sys
import joblib
from datetime import datetime as dt

import torch
import torch.nn as nn
from rdkit import RDLogger
from torch.optim import Adam, lr_scheduler

# 导入调试版本的模型
from models.graph2edits_contrastive_debug import Graph2EditsWithContrastiveDebug
from models.graph2edits import Graph2Edits
from models.model_utils import CSVLogger, get_seq_edit_accuracy
from utils.datasets import RetroEditDataset, RetroEvalDataset
from utils.mol_features import ATOM_FDIM, BOND_FDIM
from utils.rxn_graphs import Vocab

lg = RDLogger.logger()
lg.setLevel(RDLogger.CRITICAL)

DATE_TIME = dt.now().strftime('%d-%m-%Y--%H-%M-%S')
ROOT_DIR = './'
DEVICE = 'cuda' if torch.cuda.is_available() else 'cpu'

def build_model_config(args):
    """从命令行参数构建模型配置"""
    model_config = {}
    
    # 基础配置
    if args.get('use_rxn_class', False):
        atom_fdim = ATOM_FDIM + 10
    else:
        atom_fdim = ATOM_FDIM
    
    model_config['n_atom_feat'] = atom_fdim
    if args.get('atom_message', False):
        model_config['n_bond_feat'] = BOND_FDIM
    else:
        model_config['n_bond_feat'] = atom_fdim + BOND_FDIM
    
    model_config['mpn_size'] = args['mpn_size']
    model_config['mlp_size'] = args['mlp_size']
    model_config['depth'] = args['depth']
    model_config['dropout_mlp'] = args['dropout_mlp']
    model_config['dropout_mpn'] = args['dropout_mpn']
    model_config['atom_message'] = args['atom_message']
    model_config['use_attn'] = args['use_attn']
    model_config['n_heads'] = args['n_heads']
    model_config['use_rxn_class'] = args.get('use_rxn_class', False)

    return model_config

def train_epoch_contrastive_debug(args, epoch, model, train_data, loss_fn, optimizer):
    """调试版训练epoch"""
    torch.cuda.empty_cache()
    model.train()
    train_loss = 0
    train_edit_loss = 0
    train_contrastive_loss = 0
    train_acc = 0
    
    for batch_id, batch_data in enumerate(train_data):
        graph_seq_tensors, seq_labels, seq_mask = batch_data
        seq_mask = seq_mask.to(DEVICE)
        
        # 前向传播
        if args.get('use_contrastive', False):
            seq_edit_scores, contrastive_loss = model(graph_seq_tensors)
        else:
            seq_edit_scores = model(graph_seq_tensors)
            contrastive_loss = torch.tensor(0.0)

        max_seq_len, batch_size = seq_mask.size()
        seq_loss = []

        # 计算编辑预测损失
        for idx in range(max_seq_len):
            edit_labels_idx = model.to_device(seq_labels[idx])
            loss_batch = [seq_mask[idx][i] * loss_fn(seq_edit_scores[idx][i].unsqueeze(0),
                                                     torch.argmax(edit_labels_idx[i]).unsqueeze(0).long()).sum()
                          for i in range(batch_size)]

            loss = torch.stack(loss_batch, dim=0).mean()
            seq_loss.append(loss)

        edit_loss = torch.stack(seq_loss).mean()
        accuracy = get_seq_edit_accuracy(seq_edit_scores, seq_labels, seq_mask)

        # 总损失
        if args.get('use_contrastive', False):
            total_loss = edit_loss + args['contrastive_weight'] * contrastive_loss
        else:
            total_loss = edit_loss

        train_loss += total_loss.item()
        train_edit_loss += edit_loss.item()
        train_contrastive_loss += contrastive_loss.item()
        train_acc += accuracy

        # 反向传播
        optimizer.zero_grad()
        total_loss.backward()
        nn.utils.clip_grad_norm_(model.parameters(), args['max_clip'])
        optimizer.step()

        # 详细的打印信息
        if (batch_id + 1) % args['print_every'] == 0:
            if args.get('use_contrastive', False):
                print('\repoch %d/%d, batch %d/%d, total_loss: %.4f, edit_loss: %.4f, contrastive_loss: %.6f, accuracy: %.4f' % 
                      (epoch + 1, args['epochs'], batch_id + 1, len(train_data), 
                       train_loss/(batch_id + 1), train_edit_loss/(batch_id + 1), 
                       train_contrastive_loss/(batch_id + 1), train_acc/(batch_id + 1)), 
                      end='', flush=True)
                
                # 每1000个batch打印调试信息
                if (batch_id + 1) % 1000 == 0 and hasattr(model, 'debug_counters'):
                    print(f"\n[训练调试] Batch {batch_id + 1} 调试信息:")
                    for key, value in model.debug_counters.items():
                        print(f"  {key}: {value}")
            else:
                print('\repoch %d/%d, batch %d/%d, loss: %.4f, accuracy: %.4f' % 
                      (epoch + 1, args['epochs'], batch_id + 1, len(train_data), 
                       train_loss/(batch_id + 1), train_acc/(batch_id + 1)), 
                      end='', flush=True)

    train_loss = float('%.4f' % (train_loss/len(train_data)))
    train_edit_loss = float('%.4f' % (train_edit_loss/len(train_data)))
    train_contrastive_loss = float('%.6f' % (train_contrastive_loss/len(train_data)))
    train_acc = float('%.4f' % (train_acc/len(train_data)))
    
    if args.get('use_contrastive', False):
        print('\nepoch %d/%d, train_loss: %.4f, edit_loss: %.4f, contrastive_loss: %.6f, accuracy: %.4f' %
              (epoch + 1, args['epochs'], train_loss, train_edit_loss, train_contrastive_loss, train_acc))
        
        # 打印epoch结束时的调试信息
        if hasattr(model, 'debug_counters'):
            print(f"[Epoch {epoch + 1} 调试摘要]:")
            for key, value in model.debug_counters.items():
                print(f"  {key}: {value}")
    else:
        print('\nepoch %d/%d, train_loss: %.4f, accuracy: %.4f' %
              (epoch + 1, args['epochs'], train_loss, train_acc))

    return train_loss, train_edit_loss, train_contrastive_loss, train_acc

def test(model, valid_data):
    """评估函数"""
    model.eval()
    total_accuracy = 0.0
    first_step_accuracy = 0.0
    with torch.no_grad():
        for batch_id, batch_data in enumerate(valid_data):
            prod_smi_batch, edits_batch, edits_atom_batch, rxn_classes = batch_data
            for idx, prod_smi in enumerate(prod_smi_batch):
                if rxn_classes is None:
                    edits, edits_atom = model.predict(prod_smi)
                else:
                    edits, edits_atom = model.predict(
                        prod_smi, rxn_class=rxn_classes[idx])
                if edits == edits_batch[idx] and edits_atom == edits_atom_batch[idx]:
                    total_accuracy += 1.0
                if len(edits) > 0 and len(edits_batch[idx]) > 0:
                    if edits[0] == edits_batch[idx][0] and edits_atom[0] == edits_atom_batch[idx][0]:
                        first_step_accuracy += 1.0
    valid_acc = float('%.4f' % (total_accuracy/len(valid_data)))
    valid_first_step_acc = float('%.4f' % (first_step_accuracy/len(valid_data)))

    return valid_acc, valid_first_step_acc

def save_checkpoint(model, path, epoch):
    save_dict = {'state': model.state_dict()}
    if hasattr(model, 'get_saveables'):
        save_dict['saveables'] = model.get_saveables()

    name = f'epoch_{epoch + 1}.pt'
    save_file = os.path.join(path, name)
    torch.save(save_dict, save_file)

def main(args):
    # 设置输出目录
    if args.get('use_contrastive', False):
        exp_type = 'with_contrastive_debug'
    else:
        exp_type = 'without_contrastive_debug'
        
    if args.get('use_rxn_class', False):
        out_dir = os.path.join(ROOT_DIR, 'experiments', args['dataset'], 
                               'with_rxn_class', exp_type, DATE_TIME)
    else:
        out_dir = os.path.join(ROOT_DIR, 'experiments', args['dataset'], 
                               'without_rxn_class', exp_type, DATE_TIME)
    os.makedirs(out_dir, exist_ok=True)

    # 保存参数配置
    with open(os.path.join(out_dir, 'config.txt'), 'w') as f:
        for key, value in args.items():
            f.write(f'{key}: {value}\n')

    # 设置日志
    logs_filename = os.path.join(out_dir, 'logs.csv')
    if args.get('use_contrastive', False):
        fieldnames = ['epoch', 'train_acc', 'valid_acc', 'valid_first_step_acc', 
                      'train_loss', 'edit_loss', 'contrastive_loss']
    else:
        fieldnames = ['epoch', 'train_acc', 'valid_acc', 'valid_first_step_acc', 'train_loss']
    csv_logger = CSVLogger(args=args, fieldnames=fieldnames, filename=logs_filename)

    # 加载数据
    data_dir = os.path.join(ROOT_DIR, 'data', args['dataset'])
    bond_vocab_file = os.path.join(data_dir, 'train', 'bond_vocab.txt')
    atom_vocab_file = os.path.join(data_dir, 'train', 'atom_lg_vocab.txt')
    bond_vocab = Vocab(joblib.load(bond_vocab_file))
    atom_vocab = Vocab(joblib.load(atom_vocab_file))

    if args.get('use_rxn_class', False):
        train_dir = os.path.join(data_dir, 'train', 'with_rxn_class')
    else:
        train_dir = os.path.join(data_dir, 'train', 'without_rxn_class')
    eval_dir = os.path.join(data_dir, 'valid')

    train_dataset = RetroEditDataset(data_dir=train_dir)
    train_data = train_dataset.loader(
        batch_size=1, num_workers=args['num_workers'], shuffle=True)

    valid_dataset = RetroEvalDataset(
        data_dir=eval_dir, data_file='valid.file.kekulized', 
        use_rxn_class=args['use_rxn_class'])
    valid_data = valid_dataset.loader(
        batch_size=1, num_workers=args['num_workers'])

    # 构建模型
    model_config = build_model_config(args)
    
    if args.get('use_contrastive', False):
        model = Graph2EditsWithContrastiveDebug(
            config=model_config, 
            atom_vocab=atom_vocab,
            bond_vocab=bond_vocab, 
            device=DEVICE,
            contrastive_weight=args['contrastive_weight'],
            temperature=args['temperature'],
            projection_dim=args['projection_dim']
        )
        print(f'Using DEBUG contrastive learning with weight={args["contrastive_weight"]}, temperature={args["temperature"]}')
    else:
        model = Graph2Edits(
            config=model_config, 
            atom_vocab=atom_vocab,
            bond_vocab=bond_vocab, 
            device=DEVICE
        )
        print('Using baseline model without contrastive learning')
    
    print(f'Converting model to device: {DEVICE}')
    model.to(DEVICE)
    print("Param Count: ", sum([x.nelement() for x in model.parameters()]) / 10**6, "M")

    # 优化器和损失函数
    loss_fn = nn.CrossEntropyLoss(reduction='none')
    optimizer = Adam(model.parameters(), lr=args['lr'])
    scheduler = lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='max', patience=args['patience'], 
        factor=args['factor'], threshold=args['thresh'], threshold_mode='abs')

    # 训练循环
    best_acc = 0
    for epoch in range(args['epochs']):
        train_loss, edit_loss, contrastive_loss, train_acc = train_epoch_contrastive_debug(
            args, epoch, model, train_data, loss_fn, optimizer)
            
        valid_acc, valid_first_step_acc = test(model, valid_data)
        scheduler.step(valid_acc)
        
        print('epoch %d/%d, validation accuracy: %.4f, validation_first_acc: %.4f' %
              (epoch + 1, args['epochs'], valid_acc, valid_first_step_acc))
        print('---------------------------------------------------------')

        # 记录日志
        row = {
            'epoch': str(epoch + 1),
            'train_acc': str(train_acc),
            'valid_acc': str(valid_acc),
            'valid_first_step_acc': str(valid_first_step_acc),
            'train_loss': str(train_loss),
        }
        
        if args.get('use_contrastive', False):
            row['edit_loss'] = str(edit_loss)
            row['contrastive_loss'] = str(contrastive_loss)
            
        csv_logger.writerow(row)

        # 保存最佳模型
        if valid_acc >= best_acc:
            print(f'Best eval accuracy so far. Saving best model from epoch {epoch + 1} (acc={valid_acc})')
            save_checkpoint(model, out_dir, epoch)
            best_acc = valid_acc

    csv_logger.close()
    print('Experiment finished!')

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='Debug version of contrastive learning training')
    
    # 基础模型参数
    parser.add_argument('--dataset', type=str, default='uspto_50k',
                        help='dataset: uspto_50k or uspto_full')
    parser.add_argument('--use_rxn_class', default=False, action='store_true',
                        help='Whether to use rxn_class')
    parser.add_argument('--atom_message', default=False, action='store_true',
                        help='Node-level or Bond-level message passing')
    parser.add_argument('--use_attn', default=False, action='store_true',
                        help='Whether to use global attention')
    parser.add_argument('--n_heads', type=int, default=8,
                        help='Number of heads in Multihead attention')
    
    # 网络结构参数
    parser.add_argument('--mpn_size', type=int, default=256, help='MPN hidden_dim')
    parser.add_argument('--depth', type=int, default=10, help='Number of iterations')
    parser.add_argument('--dropout_mpn', type=float, default=0.15, help='MPN dropout rate')
    parser.add_argument('--mlp_size', type=int, default=512, help='MLP hidden_dim')
    parser.add_argument('--dropout_mlp', type=float, default=0.2, help='MLP dropout rate')
    
    # 训练参数
    parser.add_argument('--epochs', type=int, default=5,  # 调试时使用较少epoch
                        help='Maximum number of epochs for training')
    parser.add_argument('--lr', type=float, default=1e-3, help='learning rate')
    parser.add_argument('--patience', type=int, default=5,
                        help='Number of epochs with no improvement after which lr will be reduced')
    parser.add_argument('--factor', type=float, default=0.8,
                        help='Factor by which the lr will be reduced')
    parser.add_argument('--thresh', type=float, default=0.01,
                        help='Threshold for measuring the new optimum')
    parser.add_argument('--max_clip', type=int, default=10,
                        help='Maximum number of gradient clip')
    parser.add_argument('--print_every', type=int, default=100,  # 调试时更频繁打印
                        help='Print during train process')
    parser.add_argument('--num_workers', default=6,
                        help='Number of processes for data loading')
    
    # 对比学习相关参数
    parser.add_argument('--use_contrastive', default=False, action='store_true',
                        help='Whether to use contrastive learning')
    parser.add_argument('--contrastive_weight', type=float, default=0.1,
                        help='Weight for contrastive loss')
    parser.add_argument('--temperature', type=float, default=0.1,
                        help='Temperature for contrastive learning')
    parser.add_argument('--projection_dim', type=int, default=128,
                        help='Projection head output dimension')

    args = parser.parse_args().__dict__
    
    # 打印配置信息
    print("DEBUG Training Configuration:")
    print("=" * 50)
    for key, value in args.items():
        print(f"{key}: {value}")
    print("=" * 50)
    
    main(args)