# utils/molecular_augmentations.py 的改进版本

class MolecularAugmentations:
    def __init__(self, atom_mask_ratio=0.25, bond_delete_ratio=0.25, 
                 subgraph_remove_ratio=0.25, strategy='mix'):
        self.atom_mask_ratio = atom_mask_ratio
        self.bond_delete_ratio = bond_delete_ratio
        self.subgraph_remove_ratio = subgraph_remove_ratio
        self.strategy = strategy
    
    def augment(self, mol_graph):
        """根据策略选择增强方法"""
        if self.strategy == 'node':
            return self.atom_masking(mol_graph)
        elif self.strategy == 'subgraph':
            return self.subgraph_removal(mol_graph)
        elif self.strategy == 'mix':
            # 随机选择增强策略
            strategies = ['atom_mask', 'bond_delete', 'subgraph_remove']
            chosen_strategy = random.choice(strategies)
            
            if chosen_strategy == 'atom_mask':
                return self.atom_masking(mol_graph)
            elif chosen_strategy == 'bond_delete':
                return self.bond_deletion(mol_graph)
            else:
                return self.subgraph_removal(mol_graph)
        else:
            return mol_graph
    
    def subgraph_removal(self, mol_graph):
        """改进的子图移除 - 参考MolCLR实现"""
        augmented_graph = deepcopy(mol_graph)
        mol = augmented_graph.mol
        n_atoms = mol.GetNumAtoms()
        
        if n_atoms <= 2:
            return augmented_graph
        
        # 计算要移除的原子数量
        num_remove = max(1, int(n_atoms * self.subgraph_remove_ratio))
        
        # 随机选择起始原子
        start_atom = random.randint(0, n_atoms - 1)
        
        # BFS扩展子图
        removed_atoms = set()
        queue = [start_atom]
        
        # 构建邻接表
        adj_list = {i: [] for i in range(n_atoms)}
        for bond in mol.GetBonds():
            start_idx = bond.GetBeginAtomIdx()
            end_idx = bond.GetEndAtomIdx()
            adj_list[start_idx].append(end_idx)
            adj_list[end_idx].append(start_idx)
        
        while queue and len(removed_atoms) < num_remove:
            current_atom = queue.pop(0)
            if current_atom in removed_atoms:
                continue
                
            removed_atoms.add(current_atom)
            
            # 添加邻居到队列
            for neighbor in adj_list[current_atom]:
                if neighbor not in removed_atoms and len(removed_atoms) < num_remove:
                    queue.append(neighbor)
        
        # 掩码移除的原子
        mask_feature = [0] * len(augmented_graph.f_atoms[0])
        mask_feature[47] = 1 if len(mask_feature) > 47 else 0  # 掩码标记
        
        for atom_idx in removed_atoms:
            if atom_idx < len(augmented_graph.f_atoms):
                augmented_graph.f_atoms[atom_idx] = mask_feature
        
        return augmented_graph