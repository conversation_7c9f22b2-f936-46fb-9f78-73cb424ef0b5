# filepath: /home/<USER>/shixinyue/code/MolCLR-master/dataset/dataset.py
'''
分子对比学习(MolCLR)数据集处理文件
本文件实现了分子数据集的加载、处理和数据增强功能：
1. 从SMILES字符串创建分子图表示
2. 对分子图进行随机子图掩码(subgraph masking)作为数据增强
3. 生成正样本对(两个不同增强视图)用于对比学习
4. 组织训练和验证数据加载器

主要组件:
- MoleculeDataset: 处理单个分子，生成图表示及其增强视图
- MoleculeDatasetWrapper: 管理数据集分割和批处理加载
'''

import os
import csv
import math
import time
import random
import networkx as nx
import numpy as np
from copy import deepcopy

import torch
import torch.nn.functional as F
# from torch.utils.data import Dataset, DataLoader
from torch.utils.data.sampler import SubsetRandomSampler
import torchvision.transforms as transforms

from torch_scatter import scatter
from torch_geometric.data import Data, Dataset, DataLoader

import rdkit
from rdkit import Chem
from rdkit.Chem.rdchem import HybridizationType
from rdkit.Chem.rdchem import BondType as BT
from rdkit.Chem import AllChem


# 定义原子相关常量
ATOM_LIST = list(range(1,119))  # 元素周期表中所有元素的原子序数(1-118)

# 定义手性类型常量
CHIRALITY_LIST = [
    Chem.rdchem.ChiralType.CHI_UNSPECIFIED,  # 未指定手性
    Chem.rdchem.ChiralType.CHI_TETRAHEDRAL_CW,  # 四面体顺时针手性
    Chem.rdchem.ChiralType.CHI_TETRAHEDRAL_CCW,  # 四面体逆时针手性
    Chem.rdchem.ChiralType.CHI_OTHER  # 其他手性类型
]

# 定义化学键类型常量
BOND_LIST = [
    BT.SINGLE,  # 单键
    BT.DOUBLE,  # 双键
    BT.TRIPLE,  # 三键
    BT.AROMATIC  # 芳香键
]

# 定义键方向常量
BONDDIR_LIST = [
    Chem.rdchem.BondDir.NONE,  # 无方向
    Chem.rdchem.BondDir.ENDUPRIGHT,  # 端点向上
    Chem.rdchem.BondDir.ENDDOWNRIGHT  # 端点向下
]


def read_smiles(data_path):
    """
    从CSV文件中读取SMILES字符串
    
    参数:
        data_path (str): CSV文件路径，假设SMILES在每行的最后一列
        
    返回:
        list: 包含所有SMILES字符串的列表
    """
    smiles_data = []
    with open(data_path) as csv_file:
        csv_reader = csv.reader(csv_file, delimiter=',')
        for i, row in enumerate(csv_reader):
            smiles = row[-1]  # 获取每行最后一列作为SMILES
            smiles_data.append(smiles)
    return smiles_data


class MoleculeDataset(Dataset):
    """
    分子数据集类，用于加载和处理分子图数据
    实现对比学习所需的数据增强，通过随机子图掩码生成同一分子的两种不同视图
    
    PyTorch Geometric数据集，每次返回一个分子的两个增强视图用于对比学习
    """
    def __init__(self, data_path):
        """
        初始化分子数据集
        
        参数:
            data_path (str): 包含SMILES字符串的CSV文件路径
        """
        super(Dataset, self).__init__()
        self.smiles_data = read_smiles(data_path)

    def __getitem__(self, index):
        """
        获取单个分子样本并创建两个增强视图
        
        参数:
            index (int): 分子索引
            
        返回:
            tuple: (data_i, data_j) 同一分子的两个不同增强视图，分别进行了不同的随机子图掩码
        """
        # 从SMILES字符串构建分子对象
        mol = Chem.MolFromSmiles(self.smiles_data[index])
        # mol = Chem.AddHs(mol)  # 可选：添加氢原子

        # 获取分子的原子数和键数
        N = mol.GetNumAtoms()  # 原子数量
        M = mol.GetNumBonds()  # 化学键数量

        # 提取原子特征
        type_idx = []      # 原子类型索引
        chirality_idx = [] # 原子手性索引
        atomic_number = [] # 原子序数
        # aromatic = []    # 是否为芳香原子
        # sp, sp2, sp3, sp3d = [], [], [], []  # 杂化类型
        # num_hs = []      # 氢原子数量
        
        # 遍历所有原子，提取特征
        for atom in mol.GetAtoms():
            type_idx.append(ATOM_LIST.index(atom.GetAtomicNum()))  # 原子类型对应的索引
            chirality_idx.append(CHIRALITY_LIST.index(atom.GetChiralTag()))  # 原子手性对应的索引
            atomic_number.append(atom.GetAtomicNum())  # 原子序数
            # 以下为可选的其他原子特征
            # aromatic.append(1 if atom.GetIsAromatic() else 0)  # 是否为芳香原子
            # hybridization = atom.GetHybridization()  # 杂化类型
            # sp.append(1 if hybridization == HybridizationType.SP else 0)
            # sp2.append(1 if hybridization == HybridizationType.SP2 else 0)
            # sp3.append(1 if hybridization == HybridizationType.SP3 else 0)
            # sp3d.append(1 if hybridization == HybridizationType.SP3D else 0)

        # 构建原子特征张量
        # z = torch.tensor(atomic_number, dtype=torch.long)  # 原子序数张量
        x1 = torch.tensor(type_idx, dtype=torch.long).view(-1,1)  # 原子类型特征
        x2 = torch.tensor(chirality_idx, dtype=torch.long).view(-1,1)  # 原子手性特征
        x = torch.cat([x1, x2], dim=-1)  # 将原子类型和手性特征拼接
        
        # 可选：更多的原子特征
        # x2 = torch.tensor([atomic_number, aromatic, sp, sp2, sp3, sp3d, num_hs],
        #                     dtype=torch.float).t().contiguous()
        # x = torch.cat([x1.to(torch.float), x2], dim=-1)

        # 构建边特征和连接关系
        row, col, edge_feat = [], [], []  # 边的起点、终点和特征
        for bond in mol.GetBonds():
            # 获取键的起点和终点原子索引
            start, end = bond.GetBeginAtomIdx(), bond.GetEndAtomIdx()
            
            # 添加双向边 (无向图用两条有向边表示)
            row += [start, end]  # 边的起点
            col += [end, start]  # 边的终点
            
            # 提取键特征：类型和方向
            bond_feat = [
                BOND_LIST.index(bond.GetBondType()),  # 键类型索引
                BONDDIR_LIST.index(bond.GetBondDir())  # 键方向索引
            ]
            
            # 为双向边添加相同的特征
            edge_feat.append(bond_feat)  # 正向边特征
            edge_feat.append(bond_feat)  # 反向边特征

        # 构建PyG格式的边索引和边特征
        edge_index = torch.tensor([row, col], dtype=torch.long)  # 边连接关系
        edge_attr = torch.tensor(np.array(edge_feat), dtype=torch.long)  # 边特征

        # 随机子图掩码数据增强
        # 计算需要掩码的节点和边的数量 (25%)
        num_mask_nodes = max([1, math.floor(0.25*N)])  # 至少掩码1个节点
        num_mask_edges = max([0, math.floor(0.25*M)])  # 至少掩码0个边
        
        # 为两个视图随机选择不同的掩码节点
        mask_nodes_i = random.sample(list(range(N)), num_mask_nodes)  # 视图i的掩码节点
        mask_nodes_j = random.sample(list(range(N)), num_mask_nodes)  # 视图j的掩码节点
        
        # 为两个视图随机选择不同的掩码边
        mask_edges_i_single = random.sample(list(range(M)), num_mask_edges)  # 视图i的掩码边
        mask_edges_j_single = random.sample(list(range(M)), num_mask_edges)  # 视图j的掩码边
        
        # 将单个化学键索引转换为对应的双向边索引
        # 每个化学键对应两条有向边，索引为2*i和2*i+1
        mask_edges_i = [2*i for i in mask_edges_i_single] + [2*i+1 for i in mask_edges_i_single]
        mask_edges_j = [2*i for i in mask_edges_j_single] + [2*i+1 for i in mask_edges_j_single]

        # 创建视图i的特征和结构
        x_i = deepcopy(x)  # 复制原子特征
        
        # 掩码选定的节点，将其特征替换为特殊值
        for atom_idx in mask_nodes_i:
            x_i[atom_idx,:] = torch.tensor([len(ATOM_LIST), 0])  # 使用特殊标记表示掩码节点
        
        # 创建不包含掩码边的新边索引和特征矩阵
        edge_index_i = torch.zeros((2, 2*(M-num_mask_edges)), dtype=torch.long)
        edge_attr_i = torch.zeros((2*(M-num_mask_edges), 2), dtype=torch.long)
        
        # 复制未被掩码的边
        count = 0
        for bond_idx in range(2*M):
            if bond_idx not in mask_edges_i:  # 如果边未被掩码
                edge_index_i[:,count] = edge_index[:,bond_idx]  # 复制边连接关系
                edge_attr_i[count,:] = edge_attr[bond_idx,:]   # 复制边特征
                count += 1
                
        # 构建PyG格式的图数据对象
        data_i = Data(x=x_i, edge_index=edge_index_i, edge_attr=edge_attr_i)

        # 创建视图j的特征和结构 (与视图i过程相同，但掩码不同)
        x_j = deepcopy(x)
        for atom_idx in mask_nodes_j:
            x_j[atom_idx,:] = torch.tensor([len(ATOM_LIST), 0])
        edge_index_j = torch.zeros((2, 2*(M-num_mask_edges)), dtype=torch.long)
        edge_attr_j = torch.zeros((2*(M-num_mask_edges), 2), dtype=torch.long)
        count = 0
        for bond_idx in range(2*M):
            if bond_idx not in mask_edges_j:
                edge_index_j[:,count] = edge_index[:,bond_idx]
                edge_attr_j[count,:] = edge_attr[bond_idx,:]
                count += 1
        data_j = Data(x=x_j, edge_index=edge_index_j, edge_attr=edge_attr_j)
        
        # 返回同一分子的两个不同增强视图
        return data_i, data_j

    def __len__(self):
        """
        返回数据集中分子的数量
        
        返回:
            int: 数据集中分子的数量
        """
        return len(self.smiles_data)


class MoleculeDatasetWrapper(object):
    """
    分子数据集包装器，负责数据集的分割、批处理和加载
    
    主要功能:
    1. 将数据集分为训练集和验证集
    2. 创建对应的数据加载器
    3. 管理批处理大小、工作线程数等参数
    """
    def __init__(self, batch_size, num_workers, valid_size, data_path):
        """
        初始化数据集包装器
        
        参数:
            batch_size (int): 批处理大小
            num_workers (int): 数据加载的工作线程数
            valid_size (float): 验证集占总数据集的比例 (0-1)
            data_path (str): 数据文件路径
        """
        super(object, self).__init__()
        self.data_path = data_path
        self.batch_size = batch_size
        self.num_workers = num_workers
        self.valid_size = valid_size

    def get_data_loaders(self):
        """
        创建训练和验证数据加载器
        
        返回:
            tuple: (train_loader, valid_loader) 训练和验证数据加载器
        """
        # 创建完整数据集
        train_dataset = MoleculeDataset(data_path=self.data_path)
        # 分割并获取训练和验证数据加载器
        train_loader, valid_loader = self.get_train_validation_data_loaders(train_dataset)
        return train_loader, valid_loader

    def get_train_validation_data_loaders(self, train_dataset):
        """
        将数据集分割为训练集和验证集，并创建对应的数据加载器
        
        参数:
            train_dataset: 完整的数据集对象
            
        返回:
            tuple: (train_loader, valid_loader) 训练和验证数据加载器
        """
        # 获取数据集大小并创建索引列表
        num_train = len(train_dataset)
        indices = list(range(num_train))
        np.random.shuffle(indices)  # 随机打乱索引

        # 根据验证集比例分割数据
        split = int(np.floor(self.valid_size * num_train))
        train_idx, valid_idx = indices[split:], indices[:split]  # 前面部分作为验证集

        # 定义训练和验证集的采样器
        train_sampler = SubsetRandomSampler(train_idx)  # 训练集随机采样
        valid_sampler = SubsetRandomSampler(valid_idx)  # 验证集随机采样

        # 创建训练数据加载器
        train_loader = DataLoader(
            train_dataset, 
            batch_size=self.batch_size, 
            sampler=train_sampler,
            num_workers=self.num_workers, 
            drop_last=True  # 丢弃最后一个不完整的批次
        )

        # 创建验证数据加载器
        valid_loader = DataLoader(
            train_dataset, 
            batch_size=self.batch_size, 
            sampler=valid_sampler,
            num_workers=self.num_workers, 
            drop_last=True  # 丢弃最后一个不完整的批次
        )

        return train_loader, valid_loader
