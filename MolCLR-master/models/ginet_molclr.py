# 导入必要的库
import torch                                 # PyTorch深度学习库
from torch import nn                         # PyTorch神经网络模块
import torch.nn.functional as F              # PyTorch函数接口

# 导入PyTorch Geometric相关模块
from torch_geometric.nn import MessagePassing # 消息传递基类，图神经网络的基础
from torch_geometric.utils import add_self_loops # 为图添加自环的工具函数
from torch_geometric.nn import global_add_pool, global_mean_pool, global_max_pool # 图池化操作

# 定义原子类型的数量，包括掩码标记
num_atom_type = 119 # 包括额外的掩码标记
num_chirality_tag = 3 # 手性标记的数量

# 定义键类型和键方向的数量
num_bond_type = 5 # 包括芳香键和自环边
num_bond_direction = 3 # 键方向的数量（单向、双向、无方向）


class GINEConv(MessagePassing):
    """
    GINE卷积层实现 (Graph Isomorphism Network with Edge features)
    这是GIN的扩展版本，考虑了边特征
    """
    def __init__(self, emb_dim):
        """
        初始化GINE卷积层
        
        参数:
            emb_dim (int): 嵌入维度
        """
        super(GINEConv, self).__init__()
        # 多层感知机，用于更新节点表示
        self.mlp = nn.Sequential(
            nn.Linear(emb_dim, 2*emb_dim),  # 第一层线性变换，将维度扩大为2倍
            nn.ReLU(),                       # 非线性激活函数
            nn.Linear(2*emb_dim, emb_dim)    # 第二层线性变换，将维度变回原始大小
        )
        # 边类型的嵌入层
        self.edge_embedding1 = nn.Embedding(num_bond_type, emb_dim)
        # 边方向的嵌入层
        self.edge_embedding2 = nn.Embedding(num_bond_direction, emb_dim)
        # 使用Xavier均匀初始化权重，有助于训练稳定性
        nn.init.xavier_uniform_(self.edge_embedding1.weight.data)
        nn.init.xavier_uniform_(self.edge_embedding2.weight.data)

    def forward(self, x, edge_index, edge_attr):
        """
        前向传播函数
        
        参数:
            x (Tensor): 节点特征矩阵
            edge_index (LongTensor): 边索引
            edge_attr (Tensor): 边属性矩阵
            
        返回:
            Tensor: 更新后的节点特征
        """
        # 在边索引中添加自环（自己连接自己的边）
        edge_index = add_self_loops(edge_index, num_nodes=x.size(0))[0]

        # 为自环边添加特征
        self_loop_attr = torch.zeros(x.size(0), 2)
        self_loop_attr[:,0] = 4  # 自环边的键类型设为4
        self_loop_attr = self_loop_attr.to(edge_attr.device).to(edge_attr.dtype)
        edge_attr = torch.cat((edge_attr, self_loop_attr), dim=0)

        # 计算边嵌入向量：键类型嵌入 + 键方向嵌入
        edge_embeddings = self.edge_embedding1(edge_attr[:,0]) + self.edge_embedding2(edge_attr[:,1])

        # 调用propagate方法进行消息传递
        return self.propagate(edge_index, x=x, edge_attr=edge_embeddings)

    def message(self, x_j, edge_attr):
        """
        定义如何计算消息
        
        参数:
            x_j: 源节点的特征
            edge_attr: 边的特征
            
        返回:
            Tensor: 计算好的消息
        """
        # 消息是源节点特征与边特征的和
        return x_j + edge_attr

    def update(self, aggr_out):
        """
        定义如何更新节点表示
        
        参数:
            aggr_out: 聚合后的消息
            
        返回:
            Tensor: 更新后的节点表示
        """
        # 通过MLP更新节点表示
        return self.mlp(aggr_out)


class GINet(nn.Module):
    """
    图同构网络(Graph Isomorphism Network)模型
    
    参数:
        num_layer (int): GNN层的数量
        emb_dim (int): 嵌入维度
        feat_dim (int): 特征维度
        drop_ratio (float): dropout比率，防止过拟合
        pool (str): 池化方法，可选'mean'、'max'或'add'
        
    输出:
        节点表示和投影表示
    """
    def __init__(self, num_layer=5, emb_dim=300, feat_dim=256, drop_ratio=0, pool='mean'):
        super(GINet, self).__init__()
        self.num_layer = num_layer  # GNN层数
        self.emb_dim = emb_dim      # 嵌入维度
        self.feat_dim = feat_dim    # 特征维度
        self.drop_ratio = drop_ratio  # dropout比率

        # 原子类型的嵌入层
        self.x_embedding1 = nn.Embedding(num_atom_type, emb_dim)
        # 原子手性的嵌入层
        self.x_embedding2 = nn.Embedding(num_chirality_tag, emb_dim)
        # 使用Xavier初始化嵌入层权重
        nn.init.xavier_uniform_(self.x_embedding1.weight.data)
        nn.init.xavier_uniform_(self.x_embedding2.weight.data)

        # GINE卷积层列表
        self.gnns = nn.ModuleList()
        for layer in range(num_layer):
            self.gnns.append(GINEConv(emb_dim))

        # 批归一化层列表，用于稳定训练
        self.batch_norms = nn.ModuleList()
        for layer in range(num_layer):
            self.batch_norms.append(nn.BatchNorm1d(emb_dim))
        
        # 根据参数选择不同的池化方法
        if pool == 'mean':
            self.pool = global_mean_pool    # 平均池化
        elif pool == 'max':
            self.pool = global_max_pool     # 最大池化
        elif pool == 'add':
            self.pool = global_add_pool     # 求和池化
        
        # 特征线性层，将嵌入维度转换为特征维度
        self.feat_lin = nn.Linear(self.emb_dim, self.feat_dim)

        # 投影头，用于对比学习
        self.out_lin = nn.Sequential(  
            nn.Linear(self.feat_dim, self.feat_dim),  # 第一个线性层
            nn.ReLU(inplace=True),                    # ReLU激活函数
            nn.Linear(self.feat_dim, self.feat_dim//2)  # 第二个线性层，降维
        )

    def forward(self, data):
        """
        前向传播函数
        
        参数:
            data: 包含图数据的数据对象，至少包含节点特征、边索引和边属性
            
        返回:
            tuple: (h, out) - h是图表示，out是投影后的表示用于对比学习
        """
        # 获取输入数据
        x = data.x                # 节点特征
        edge_index = data.edge_index  # 边索引
        edge_attr = data.edge_attr    # 边属性

        # 结合原子类型和手性的嵌入向量
        h = self.x_embedding1(x[:,0]) + self.x_embedding2(x[:,1])

        # 通过多层GNN进行消息传递
        for layer in range(self.num_layer):
            h = self.gnns[layer](h, edge_index, edge_attr)  # GNN层传播
            h = self.batch_norms[layer](h)  # 批归一化
            if layer == self.num_layer - 1:
                # 最后一层只应用dropout
                h = F.dropout(h, self.drop_ratio, training=self.training)
            else:
                # 中间层应用ReLU激活和dropout
                h = F.dropout(F.relu(h), self.drop_ratio, training=self.training)

        # 图池化，将节点特征聚合为图特征
        h = self.pool(h, data.batch)
        # 特征变换
        h = self.feat_lin(h)
        # 投影变换，用于对比学习
        out = self.out_lin(h)
        
        # 返回图表示和投影表示
        return h, out
