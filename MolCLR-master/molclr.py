import os                  # 提供与操作系统交互的功能，如文件路径操作
import shutil              # 提供高级文件操作，如文件复制
import sys                 # 提供系统相关的功能和变量
import torch               # PyTorch深度学习库
import yaml                # YAML文件解析库，用于配置文件读取
import numpy as np         # 科学计算库
from datetime import datetime  # 日期时间处理库

import torch.nn.functional as F                # PyTorch中的函数模块，提供激活函数等
from torch.utils.tensorboard import SummaryWriter  # TensorBoard可视化工具
from torch.optim.lr_scheduler import CosineAnnealingLR  # 余弦退火学习率调度器

from utils.nt_xent import NTXentLoss  # NT-Xent (Normalized Temperature-scaled Cross Entropy Loss)对比学习损失函数


# 检查是否支持NVIDIA Apex混合精度训练
apex_support = False
try:
    sys.path.append('./apex')  # 添加Apex库路径
    from apex import amp      # 导入自动混合精度模块

    apex_support = True       # 标记支持Apex
except:
    print("Please install apex for mixed precision training from: https://github.com/NVIDIA/apex")
    apex_support = False      # 标记不支持Apex


# 保存配置文件的函数
def _save_config_file(model_checkpoints_folder):
    # 如果检查点文件夹不存在则创建
    if not os.path.exists(model_checkpoints_folder):
        os.makedirs(model_checkpoints_folder)
        # 将配置文件复制到检查点文件夹中
        shutil.copy('./config.yaml', os.path.join(model_checkpoints_folder, 'config.yaml'))


# MolCLR主类：分子对比学习表示(Molecular Contrastive Learning of Representations)
class MolCLR(object):
    def __init__(self, dataset, config):
        self.config = config          # 存储配置
        self.device = self._get_device()  # 获取计算设备(CPU/GPU)
        
        # 创建日志目录，使用当前日期时间命名
        dir_name = datetime.now().strftime('%b%d_%H-%M-%S')
        log_dir = os.path.join('ckpt', dir_name)
        self.writer = SummaryWriter(log_dir=log_dir)  # TensorBoard记录器

        self.dataset = dataset  # 存储数据集
        # 初始化NT-Xent损失函数
        self.nt_xent_criterion = NTXentLoss(self.device, config['batch_size'], **config['loss'])

    # 获取计算设备的方法
    def _get_device(self):
        # 如果有可用GPU且配置未指定使用CPU
        if torch.cuda.is_available() and self.config['gpu'] != 'cpu':
            device = self.config['gpu']
            torch.cuda.set_device(device)  # 设置使用指定GPU
        else:
            device = 'cpu'  # 否则使用CPU
        print("Running on:", device)

        return device

    # 单步训练方法
    def _step(self, model, xis, xjs, n_iter):
        # 获取第一组增强数据的表示和投影
        ris, zis = model(xis)  # [N,C] - N是批次大小，C是特征维度

        # 获取第二组增强数据的表示和投影
        rjs, zjs = model(xjs)  # [N,C]

        # 归一化投影特征向量
        zis = F.normalize(zis, dim=1)  # 在特征维度上进行L2归一化
        zjs = F.normalize(zjs, dim=1)

        # 计算对比损失
        loss = self.nt_xent_criterion(zis, zjs)
        return loss

    # 训练方法
    def train(self):
        # 获取训练和验证数据加载器
        train_loader, valid_loader = self.dataset.get_data_loaders()

        # 根据配置选择模型类型
        if self.config['model_type'] == 'gin':
            # GIN (Graph Isomorphism Network)
            from models.ginet_molclr import GINet
            model = GINet(**self.config["model"]).to(self.device)
            model = self._load_pre_trained_weights(model)  # 加载预训练权重
        elif self.config['model_type'] == 'gcn':
            # GCN (Graph Convolutional Network)
            from models.gcn_molclr import GCN
            model = GCN(**self.config["model"]).to(self.device)
            model = self._load_pre_trained_weights(model)
        else:
            raise ValueError('Undefined GNN model.')
        print(model)  # 打印模型结构
        
        # 初始化优化器
        optimizer = torch.optim.Adam(
            model.parameters(), self.config['init_lr'],  # 使用初始学习率
            weight_decay=eval(self.config['weight_decay'])  # 权重衰减
        )
        # 初始化学习率调度器：余弦退火
        scheduler = CosineAnnealingLR(
            optimizer, T_max=self.config['epochs']-self.config['warm_up'],  # 总周期减去预热周期
            eta_min=0, last_epoch=-1  # 最小学习率和起始周期
        )

        # 如果支持Apex且配置开启了FP16混合精度
        if apex_support and self.config['fp16_precision']:
            model, optimizer = amp.initialize(
                model, optimizer, opt_level='O2', keep_batchnorm_fp32=True
            )

        # 创建模型检查点文件夹
        model_checkpoints_folder = os.path.join(self.writer.log_dir, 'checkpoints')

        # 保存配置文件
        _save_config_file(model_checkpoints_folder)

        # 初始化计数器和最佳验证损失
        n_iter = 0  # 训练迭代次数
        valid_n_iter = 0  # 验证迭代次数
        best_valid_loss = np.inf  # 最佳验证损失初始化为无穷大

        # 开始训练循环
        for epoch_counter in range(self.config['epochs']):
            for bn, (xis, xjs) in enumerate(train_loader):
                optimizer.zero_grad()  # 清零梯度

                # 将数据移到指定设备
                xis = xis.to(self.device)
                xjs = xjs.to(self.device)

                # 计算损失
                loss = self._step(model, xis, xjs, n_iter)

                # 按设定频率记录训练信息
                if n_iter % self.config['log_every_n_steps'] == 0:
                    self.writer.add_scalar('train_loss', loss, global_step=n_iter)  # 记录训练损失
                    self.writer.add_scalar('cosine_lr_decay', scheduler.get_last_lr()[0], global_step=n_iter)  # 记录学习率
                    print(epoch_counter, bn, loss.item())  # 打印训练信息

                # 反向传播
                if apex_support and self.config['fp16_precision']:
                    with amp.scale_loss(loss, optimizer) as scaled_loss:  # 使用Apex缩放损失
                        scaled_loss.backward()
                else:
                    loss.backward()  # 标准反向传播

                optimizer.step()  # 更新参数
                n_iter += 1  # 迭代计数器递增

            # 按设定频率进行模型验证
            if epoch_counter % self.config['eval_every_n_epochs'] == 0:
                valid_loss = self._validate(model, valid_loader)  # 计算验证损失
                print(epoch_counter, bn, valid_loss, '(validation)')
                if valid_loss < best_valid_loss:
                    # 如果验证损失更好，保存模型权重
                    best_valid_loss = valid_loss
                    torch.save(model.state_dict(), os.path.join(model_checkpoints_folder, 'model.pth'))
            
                # 记录验证损失
                self.writer.add_scalar('validation_loss', valid_loss, global_step=valid_n_iter)
                valid_n_iter += 1  # 验证迭代计数器递增
            
            # 按设定频率保存模型检查点
            if (epoch_counter+1) % self.config['save_every_n_epochs'] == 0:
                torch.save(model.state_dict(), os.path.join(model_checkpoints_folder, 'model_{}.pth'.format(str(epoch_counter))))

            # 学习率预热：在预热期结束后开始使用学习率调度器
            if epoch_counter >= self.config['warm_up']:
                scheduler.step()  # 更新学习率

    # 加载预训练权重的方法
    def _load_pre_trained_weights(self, model):
        try:
            # 尝试加载预训练权重
            checkpoints_folder = os.path.join('./ckpt', self.config['load_model'], 'checkpoints')
            state_dict = torch.load(os.path.join(checkpoints_folder, 'model.pth'))
            model.load_state_dict(state_dict)  # 加载权重
            print("Loaded pre-trained model with success.")
        except FileNotFoundError:
            # 如果找不到预训练权重文件，就从头开始训练
            print("Pre-trained weights not found. Training from scratch.")

        return model

    # 模型验证方法
    def _validate(self, model, valid_loader):
        # 禁用梯度计算
        with torch.no_grad():
            model.eval()  # 设置模型为评估模式

            valid_loss = 0.0  # 验证损失累加器
            counter = 0  # 样本计数器
            for (xis, xjs) in valid_loader:
                # 将数据移到指定设备
                xis = xis.to(self.device)
                xjs = xjs.to(self.device)

                # 计算验证损失
                loss = self._step(model, xis, xjs, counter)
                valid_loss += loss.item()  # 累加损失
                counter += 1  # 批次计数器递增
            valid_loss /= counter  # 计算平均验证损失
        
        model.train()  # 将模型设回训练模式
        return valid_loss  # 返回平均验证损失


# 主函数
def main():
    # 加载配置文件
    config = yaml.load(open("config.yaml", "r"), Loader=yaml.FullLoader)
    print(config)  # 打印配置信息

    # 根据配置选择不同的数据增强方式
    if config['aug'] == 'node':
        # 节点级数据增强
        from dataset.dataset import MoleculeDatasetWrapper
    elif config['aug'] == 'subgraph':
        # 子图级数据增强
        from dataset.dataset_subgraph import MoleculeDatasetWrapper
    elif config['aug'] == 'mix':
        # 混合数据增强
        from dataset.dataset_mix import MoleculeDatasetWrapper
    else:
        raise ValueError('Not defined molecule augmentation!')

    # 初始化数据集
    dataset = MoleculeDatasetWrapper(config['batch_size'], **config['dataset'])
    # 初始化MolCLR对象
    molclr = MolCLR(dataset, config)
    # 开始训练
    molclr.train()


# 脚本入口点
if __name__ == "__main__":
    main()  # 执行主函数
