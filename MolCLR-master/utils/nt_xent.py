# filepath: /home/<USER>/shixinyue/code/MolCLR-master/utils/nt_xent.py
'''
NT-Xent (Normalized Temperature-scaled Cross Entropy) 损失函数实现
这是对比学习中常用的损失函数，也称为InfoNCE损失

这个损失函数的核心思想是：
1. 最大化正样本对（同一数据的不同增强视图）之间的相似性
2. 最小化负样本对（不同数据的增强视图）之间的相似性
3. 使用温度系数来调整相似度分布的平滑程度

在MolCLR中，正样本对由同一分子的两个不同随机子图掩码增强视图组成
'''

import torch
import numpy as np


class NTXentLoss(torch.nn.Module):
    '''
    NT-Xent损失函数类
    
    参数:
        device: 计算设备(CPU/GPU)
        batch_size: 批次大小
        temperature: 温度参数，控制相似度分布的平滑程度
        use_cosine_similarity: 是否使用余弦相似度（否则使用点积相似度）
    '''
    def __init__(self, device, batch_size, temperature, use_cosine_similarity):
        super(NTXentLoss, self).__init__()
        self.batch_size = batch_size
        self.temperature = temperature  # 温度参数，控制分布平滑程度
        self.device = device
        self.softmax = torch.nn.Softmax(dim=-1)
        # 创建用于过滤相关样本的掩码
        self.mask_samples_from_same_repr = self._get_correlated_mask().type(torch.bool)
        # 根据参数选择相似度计算函数
        self.similarity_function = self._get_similarity_function(use_cosine_similarity)
        # 使用交叉熵损失作为基础损失函数
        self.criterion = torch.nn.CrossEntropyLoss(reduction="sum")

    def _get_similarity_function(self, use_cosine_similarity):
        '''
        根据参数返回相应的相似度计算函数
        
        参数:
            use_cosine_similarity: 是否使用余弦相似度
            
        返回:
            选定的相似度计算函数
        '''
        if use_cosine_similarity:
            self._cosine_similarity = torch.nn.CosineSimilarity(dim=-1)
            return self._cosine_simililarity
        else:
            return self._dot_simililarity

    def _get_correlated_mask(self):
        '''
        创建用于标识正样本对的掩码矩阵
        
        返回:
            掩码张量，标识哪些位置是负样本（值为True的位置）
        '''
        # 创建对角线矩阵，标识自身对自身的相似性
        diag = np.eye(2 * self.batch_size)
        # 创建平移对角线矩阵，标识第i个样本与第i+batch_size个样本的相似性（正样本对）
        l1 = np.eye((2 * self.batch_size), 2 * self.batch_size, k=-self.batch_size)
        l2 = np.eye((2 * self.batch_size), 2 * self.batch_size, k=self.batch_size)
        # 合并三个矩阵，得到所有需要排除的位置（自身和正样本对）
        mask = torch.from_numpy((diag + l1 + l2))
        # 取反，得到负样本的位置（值为True的位置）
        mask = (1 - mask).type(torch.bool)
        return mask.to(self.device)

    @staticmethod
    def _dot_simililarity(x, y):
        '''
        计算点积相似度
        
        参数:
            x, y: 输入特征向量
            
        返回:
            点积相似度矩阵
        '''
        v = torch.tensordot(x.unsqueeze(1), y.T.unsqueeze(0), dims=2)
        # x shape: (N, 1, C)
        # y shape: (1, C, 2N)
        # v shape: (N, 2N)
        return v

    def _cosine_simililarity(self, x, y):
        '''
        计算余弦相似度
        
        参数:
            x, y: 输入特征向量
            
        返回:
            余弦相似度矩阵
        '''
        # x shape: (N, 1, C)
        # y shape: (1, 2N, C)
        # v shape: (N, 2N)
        v = self._cosine_similarity(x.unsqueeze(1), y.unsqueeze(0))
        return v

    def forward(self, zis, zjs):
        '''
        计算NT-Xent损失
        
        参数:
            zis: 第一组增强视图的特征表示，形状为[N, D]
            zjs: 第二组增强视图的特征表示，形状为[N, D]
            
        返回:
            计算得到的对比损失值
        '''
        # 将两组视图的特征拼接在一起
        representations = torch.cat([zjs, zis], dim=0)  # shape: [2N, D]

        # 计算所有表示之间的相似度矩阵
        similarity_matrix = self.similarity_function(representations, representations)

        # 提取正样本对的相似度分数
        l_pos = torch.diag(similarity_matrix, self.batch_size)  # 第一组视图与第二组视图的对角线相似度
        r_pos = torch.diag(similarity_matrix, -self.batch_size)  # 第二组视图与第一组视图的对角线相似度
        positives = torch.cat([l_pos, r_pos]).view(2 * self.batch_size, 1)  # 所有正样本对的相似度

        # 提取负样本对的相似度分数
        negatives = similarity_matrix[self.mask_samples_from_same_repr].view(2 * self.batch_size, -1)

        # 将正样本和负样本的相似度拼接在一起作为logits
        logits = torch.cat((positives, negatives), dim=1)  # 第一列是正样本相似度，其余列是负样本相似度
        # 应用温度缩放
        logits /= self.temperature

        # 创建标签：对于每个样本，正样本对的索引为0（即第一列）
        labels = torch.zeros(2 * self.batch_size).to(self.device).long()
        # 计算交叉熵损失
        loss = self.criterion(logits, labels)

        # 对损失进行归一化，除以批次中的样本总数
        return loss / (2 * self.batch_size)
