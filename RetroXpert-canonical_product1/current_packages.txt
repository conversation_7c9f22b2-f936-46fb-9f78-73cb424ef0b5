absl-py==1.4.0
cachetools==4.2.4
certifi==2025.1.31
charset-normalizer==2.0.12
click==8.0.4
colorama==0.4.5
ConfigArgParse==1.7
cycler @ file:///home/<USER>/feedstock_root/build_artifacts/cycler_1635519461629/work
dataclasses @ file:///home/<USER>/feedstock_root/build_artifacts/dataclasses_1628958435052/work
decorator @ file:///home/<USER>/feedstock_root/build_artifacts/decorator_1641555617451/work
dgl==0.4.2
Flask==2.0.3
future==1.0.0
google-auth==2.22.0
google-auth-oauthlib==0.4.6
grpcio==1.48.2
idna==3.10
importlib-metadata==4.8.3
itsdangerous==2.0.1
Jinja2==3.0.3
joblib @ file:///home/<USER>/feedstock_root/build_artifacts/joblib_1663332044897/work
kiwisolver @ file:///home/<USER>/feedstock_root/build_artifacts/kiwisolver_1610099771815/work
Markdown==3.3.7
MarkupSafe==2.0.1
matplotlib==3.1.2
mkl-fft==1.0.15
mkl-random==1.1.0
mkl-service==2.3.0
networkx==2.4
numpy==1.18.1
oauthlib==3.2.2
olefile @ file:///home/<USER>/feedstock_root/build_artifacts/olefile_1602866521163/work
# Editable install with no version control (OpenNMT-py==1.0.0rc2)
-e /home/<USER>/shixinyue/code/RetroXpert-canonical_product1/OpenNMT-py
pandas==1.0.0
Pillow @ file:///tmp/build/80754af9/pillow_1625670622947/work
portalocker==2.7.0
protobuf==3.19.6
pyasn1==0.5.1
pyasn1-modules==0.3.0
pycairo==1.20.1
pyonmttok==1.37.1
pyparsing @ file:///home/<USER>/feedstock_root/build_artifacts/pyparsing_1724616129934/work
python-dateutil @ file:///home/<USER>/feedstock_root/build_artifacts/python-dateutil_1626286286081/work
pytz @ file:///home/<USER>/feedstock_root/build_artifacts/pytz_1693930252784/work
PyYAML==6.0.1
regex==2023.8.8
requests==2.27.1
requests-oauthlib==2.0.0
rsa==4.9
sacrebleu==2.1.0
scikit-learn==0.22.1
scipy @ file:///home/<USER>/feedstock_root/build_artifacts/scipy_1604304777848/work
sentencepiece==0.2.0
six @ file:///home/<USER>/feedstock_root/build_artifacts/six_1620240208055/work
tabulate==0.8.10
tensorboard==2.10.1
tensorboard-data-server==0.6.1
tensorboard-plugin-wit==1.8.1
torchtext==0.4.0
tornado @ file:///home/<USER>/feedstock_root/build_artifacts/tornado_1610094701020/work
tqdm==4.30.0
typing_extensions @ file:///home/<USER>/feedstock_root/build_artifacts/typing_extensions_1644850595256/work
urllib3==1.26.20
waitress==2.0.0
Werkzeug==2.0.3
zipp==3.6.0
