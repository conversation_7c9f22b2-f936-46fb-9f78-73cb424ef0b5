# This file may be used to create an environment using:
# $ conda create --name <env> --file <this file>
# platform: linux-64
absl-py==1.4.0
cachetools==4.2.4
certifi==2024.12.14
charset-normalizer==2.0.12
click==8.0.4
ConfigArgParse==1.7
cycler==0.11.0
dataclasses==0.8
decorator==4.4.2
dgl==0.4.2
Flask==2.0.3
future==1.0.0
google-auth==2.22.0
google-auth-oauthlib==0.4.6
grpcio==1.48.2
idna==3.10
importlib-metadata==4.8.3
itsdangerous==2.0.1
Jinja2==3.0.3
joblib==1.1.1
kiwisolver==1.3.1
Markdown==3.3.7
MarkupSafe==2.0.1
matplotlib==3.1.2
mkl-fft==1.0.15
mkl-random==1.1.0
mkl-service==2.3.0
networkx==2.4
numpy==1.18.1
oauthlib==3.2.2
olefile @ file:///home/<USER>/feedstock_root/build_artifacts/olefile_1602866521163/work
# Editable install with no version control (OpenNMT-py==1.0.0rc2)
-e /home/<USER>/shixinyue/code/RetroXpert-canonical_product/OpenNMT-py
pandas==1.0.0
Pillow==7.0.0
protobuf==3.19.6
pyasn1==0.5.1
pyasn1-modules==0.3.0
pycairo==1.20.1
pyonmttok==1.37.1
pyparsing==3.1.4
python-dateutil @ file:///home/<USER>/feedstock_root/build_artifacts/python-dateutil_1626286286081/work
pytz @ file:///home/<USER>/feedstock_root/build_artifacts/pytz_1693930252784/work
requests==2.27.1
requests-oauthlib==2.0.0
rsa==4.9
scikit-learn==0.22.1
scipy @ file:///home/<USER>/feedstock_root/build_artifacts/scipy_1604304777848/work
six @ file:///home/<USER>/feedstock_root/build_artifacts/six_1620240208055/work
tensorboard==2.10.1
tensorboard-data-server==0.6.1
tensorboard-plugin-wit==1.8.1
torch==1.8.1+cu111
torchtext==0.4.0
torchvision==0.9.1+cu111
tqdm==4.30.0
typing_extensions==4.1.1
urllib3==1.26.20
Werkzeug==2.0.3
zipp==3.6.0