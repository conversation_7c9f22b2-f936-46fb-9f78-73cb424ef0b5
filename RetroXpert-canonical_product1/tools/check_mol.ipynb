{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["%matplotlib inline  \n", "import matplotlib.pyplot as plt\n", "\n", "\n", "\n", "import pandas as pd\n", "\n", "\n", "file = r'C:\\Users\\<USER>\\Documents\\retrosynthesis\\retrosim\\retrosim\\data\\data_split.csv'\n", "\n", "data = pd.read_csv(file) \n", "\n", "\n", "rxn = data['rxn_smiles'].tolist()\n", "\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["from copy import copy\n", "\n", "from rdkit import Chem\n", "from rdkit.Chem import rdChemReactions, rdDepictor, Draw\n", "from rdkit.Chem.Draw import rdMolDraw2D, DrawingOptions\n", "\n", "\n", "def get_tagged_atoms_from_mol(mol):\n", "    '''Takes an RDKit molecule and returns list of tagged atoms and their\n", "    corresponding numbers'''\n", "    atoms = []\n", "    atom_tags = []\n", "    for atom in mol.GetAtoms():\n", "        if atom.HasProp('molAtomMapNumber'):\n", "            atoms.append(atom)\n", "            atom_tags.append(str(atom.GetProp('molAtomMapNumber')))\n", "    return atoms, atom_tags\n", "\n", "\n", "\n", "for i, r in enumerate(rxn[:1000]):\n", "    s, t = r.split('>>')\n", "    mol = Chem.MolFromSmiles(t)\n", "    if mol is None:\n", "        print('None mol:', t)\n", "\n", "    prod_atoms, prod_atom_tags = get_tagged_atoms_from_mol(mol)\n", "    reactants = s.split('.')\n", "    react_mols, react_smis = [mol], ['product']\n", "\n", "    for j, reactant in enumerate(reactants):\n", "        mol_react = Chem.MolFromSmiles(reactant)\n", "        if mol_react is None:\n", "            print('None mol:', reactant)\n", "            continue\n", "\n", "        react_mols.append(mol_react)\n", "        react_smis.append('reactant_{}'.format(j))\n", "\n", "    img = Draw.MolsToGridImage(\n", "        react_mols,\n", "        molsPerRow=len(react_mols),\n", "        subImgSize=(300, 300),\n", "        legends=react_smis\n", "    )\n", "    img.save('plots_rxn/rxn_{}_synthon.png'.format(i))\n", "    \n"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<rdkit.Chem.rdchem.Mol at 0x1f0b33a60d0>"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["mol_new\n"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["COC(=O)[C@H](CCCC[NH])NC(=O)Nc1cc(OC)cc(C(C)(C)C)c1O\n"]}], "source": ["print(Chem.MolToSmiles(mol_new))"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<rdkit.Chem.rdchem.Mol at 0x2731acfbdf0>"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["from rdkit import Chem\n", "\n", "\n", "smi = 'O=C(c1ccc(-c2ccc(-c3cccs3)cc2)c2ccc(Cl)cc12)c1ccccc1'\n", "mol = Chem.MolFromSmiles(smi)\n", "\n", "mol"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<rdkit.Chem.rdchem.Mol at 0x2731ad5ddf0>"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "smi = 'C1=C[NH+]=NC=N1'\n", "mol = Chem.MolFromSmiles(smi)\n", "\n", "mol"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.9"}}, "nbformat": 4, "nbformat_minor": 2}