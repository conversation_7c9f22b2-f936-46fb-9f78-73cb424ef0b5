{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "prediction = r'D:\\retrosynthesis\\OpenNMT-py\\experiments\\results\\predictions_on_USPTO50K_test.csv'\n", "\n", "df = pd.read_csv(prediction)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Second phase Top1 acc =  0.6560814859197124\n"]}], "source": ["from rdkit import Chem\n", "from rdkit.Chem import Draw\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "pred_results = dict()\n", "for idx, row in df.iterrows():\n", "    idx = row['index']\n", "    product = row['product']\n", "    synthon = row['synthon']\n", "    target = row['target']\n", "    pred = row['canonical_prediction_1']\n", "    \n", "    if idx not in pred_results:\n", "        pred_results[idx] = {'product': product, 'synthons': []}\n", "    pred_results[idx]['synthons'].append([synthon, target, pred])\n", "\n", "\n", "pred_results_2th = []\n", "for idx, rxns in pred_results.items():\n", "    flag = True\n", "    pred_results[idx]['okay'] = True\n", "    for rxn in rxns['synthons']:\n", "        if rxn[1] != rxn[2]:\n", "            flag = False\n", "            pred_results[idx]['okay'] = False\n", "    pred_results_2th.append(flag)\n", "\n", "print('Second phase Top1 acc = ', sum(pred_results_2th) / len(pred_results_2th))\n", "\n", "\n", "for idx, rxns in pred_results.items():\n", "    if not pred_results[idx]['okay']:\n", "        continue\n", "    names = ['prod']\n", "    smis = [rxns['product']]\n", "    for i, smi in enumerate(pred_results[idx]['synthons']):\n", "        names.append('synthon_{}'.format(i + 1))\n", "        smis.append(smi[0])\n", "        names.append('target_{}'.format(i + 1))\n", "        smis.append(smi[1])\n", "        names.append('pred_{}'.format(i + 1))\n", "        smis.append(smi[2])\n", "    \n", "    for name, smi in zip(names, smis):\n", "        try:\n", "            mol = Chem.MolFromSmiles(smi, sanitize=False)\n", "            fig = Draw.MolToMPL(mol, kekulize=True, size=(500, 500))\n", "            fig.suptitle(smi, fontsize=20, x=1.25, y=0.1)\n", "            fig.savefig('D:/retrosynthesis/plots/{}_{}_{}.png'.format(idx, name, str(pred_results[idx]['okay'])), bbox_inches='tight')\n", "            plt.cla()\n", "            plt.clf()\n", "            plt.close()\n", "        except:\n", "            pass\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import os\n", "import matplotlib.pyplot as plt\n", "import matplotlib.image as mpimg\n", "\n", "\n", "error_pred_path = 'D:/retrosynthesis/plots/'\n", "figs = [f for f in os.listdir(error_pred_path) if os.path.isfile(os.path.join(error_pred_path, f))]\n", "\n", "names_dict = dict()\n", "for name in figs:\n", "    items = name.split('_')\n", "    if items[0] not in names_dict:\n", "        names_dict[items[0]] = []\n", "    names_dict[items[0]].append(name)\n", "    \n", "\n", "for key, items in names_dict.items():\n", "    if len(items) not in [4, 7, 10, 13, 16]:\n", "        continue\n", "    flag = items[0].split('_')[-1][:-4]\n", "    num_reactants = (len(items) - 1) // 3\n", "    \n", "    prod = '{}_prod_{}.png'.format(key, flag)\n", "    plt.figure(figsize=(20, 10))\n", "    plt.subplot(num_reactants + 1, 3, 2)\n", "    plt.imshow(mpimg.imread(os.path.join(error_pred_path, prod)))\n", "    plt.tight_layout()\n", "    plt.axis('off')\n", "    \n", "    for i in range(num_reactants):\n", "        synthon = '{}_synthon_{}_{}.png'.format(key, i + 1, flag)\n", "        plt.subplot(num_reactants + 1, 3, 3 * i + 4)\n", "        plt.imshow(mpimg.imread(os.path.join(error_pred_path, synthon)))\n", "        plt.tight_layout()\n", "        plt.axis('off')\n", "        \n", "        synthon = '{}_target_{}_{}.png'.format(key, i + 1, flag)\n", "        plt.subplot(num_reactants + 1, 3, 3 * i + 4)\n", "        plt.imshow(mpimg.imread(os.path.join(error_pred_path, synthon)))\n", "        plt.tight_layout()\n", "        plt.axis('off')\n", "        \n", "        synthon = '{}_pred_{}_{}.png'.format(key, i + 1, flag)\n", "        plt.subplot(num_reactants + 1, 3, 3 * i + 4)\n", "        plt.imshow(mpimg.imread(os.path.join(error_pred_path, synthon)))\n", "        plt.tight_layout()\n", "        plt.axis('off')\n", "        \n", "    plt.show()\n", "        \n", "\n", "# for p, t in zip(pred, tgt):\n", "#     if p.split('_')[0] != t.split('_')[0]:\n", "#         print(p, t)\n", "#         continue\n", "#     plt.figure(figsize=(20, 10))\n", "#     plt.subplot(1, 2, 1)\n", "#     plt.imshow(mpimg.imread(os.path.join(error_pred_path, p)))\n", "#     plt.tight_layout()\n", "#     plt.axis('off')\n", " \n", "#     plt.subplot(1, 2, 2)\n", "#     plt.imshow(mpimg.imread(os.path.join(error_pred_path, t)))\n", "#     plt.tight_layout()\n", "#     plt.axis('off')\n", "    \n", "#     #plt.show()\n", "#     plt.savefig('D:/error_pred_merge/error_{}'.format(p), bbox_inches='tight')\n", "#     plt.close()\n", "\n", "\n", "    \n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.9"}}, "nbformat": 4, "nbformat_minor": 2}