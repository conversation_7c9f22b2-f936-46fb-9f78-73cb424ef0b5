{"cells": [{"cell_type": "code", "execution_count": 87, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["40008 5007 5001\n"]}], "source": ["import pandas as pd\n", "\n", "file = r'C:\\Users\\<USER>\\Documents\\retrosynthesis\\retrosim\\retrosim\\data\\data_split.csv'\n", "\n", "data = pd.read_csv(file) \n", "\n", "prod_smiles = data['prod_smiles'].tolist()\n", "split =  data['dataset'].tolist()\n", "rxn = data['rxn_smiles'].tolist()\n", "\n", "products = {'train': [], 'test': [], 'val': []}\n", "for s, product in zip(split, prod_smiles):\n", "    products[s].append(product)\n", "\n", "print(len(products['train']), len(products['test']), len(products['val']))\n"]}, {"cell_type": "code", "execution_count": 84, "metadata": {}, "outputs": [], "source": ["import os\n", "import re\n", "import sys\n", "%matplotlib inline\n", "import matplotlib.pyplot as plt\n", "\n", "from collections import Counter\n", "from rdkit import Chem\n", "from rdkit.Chem import Draw\n", "from rdkit.Chem import RDConfig\n", "sys.path.append(os.path.join(RDConfig.RDContribDir, 'IFG'))\n", "import ifg\n", "\n", "\n", "# A utility function to find the subset of an element i \n", "def find_parent(parent,i): \n", "    if parent[i] == -1: \n", "        return i \n", "    else:\n", "        return find_parent(parent, parent[i]) \n", "    \n", "# A utility function to do union of two subsets \n", "def union(parent, x, y): \n", "    x_set = find_parent(parent, x) \n", "    y_set = find_parent(parent, y) \n", "    parent[x_set] = y_set \n", "    return parent\n", "\n", "ring_counter = Counter()\n", "token = Counter()\n", "for k, smiles in enumerate(products['train'][:]):\n", "    mol = Chem.Mo<PERSON>rom<PERSON>(smiles)\n", "    #[a.<PERSON>ap<PERSON>(0) for a in mol.GetAtoms()]\n", "    \n", "    # Find rings\n", "    highlights = []\n", "    colors = {}\n", "    rings = Chem.GetSymmSSSR(mol)\n", "    for ring in rings:\n", "        #print('Atom Index ring:', list(ring))\n", "        highlights += list(ring)\n", "        for r in ring:\n", "            colors[r] = (1,0,0)\n", "    \n", "#     rings = mol.GetRingInfo().BondRings()\n", "#     for ring in rings:\n", "#         #print('BondRings:', ring)\n", "#         submol = Chem.PathToSubmol(mol, ring)\n", "#         ringsmi = Chem.MolToSmiles(submol)\n", "#         ring_counter.update([ringsmi])\n", "\n", "    rings = mol.GetRingInfo().AtomRings()\n", "    for ring in rings:\n", "        emol = Chem.EditableMol(mol)\n", "        atoms_to_remove = [a.GetIdx() for a in mol.GetAtoms()]\n", "        atoms_to_remove = [a for a in atoms_to_remove if a not in ring]\n", "        # Indices are only changed if they are higher than the removed index.\n", "        for index in sorted(atoms_to_remove, reverse=True):\n", "            emol.RemoveAtom(index)\n", "        smi = Chem.MolToSmiles(emol.GetMol(), isomericSmiles=True)\n", "\n", "        token.update(smi)\n", "        ringsmi = smi.lower()\n", "        ringsmi = re.sub('[!h#=+-]', '', ringsmi)\n", "        ringsmi = ringsmi.replace('[', '')\n", "        ringsmi = ringsmi.replace(']', '')\n", "        ringsmi = ringsmi.replace('2', '')\n", "        ring_counter.update([ringsmi])\n", "    \n", "   \n", "    continue\n", "    \n", "    img = Draw.MolToImage(mol, size=(300, 300), highlightAtoms=highlights)\n", "    plt.imshow(img)\n", "    plt.tight_layout()\n", "    plt.axis('off')\n", "    plt.show()\n", "    \n", "    continue\n", "    \n", "    # Find functional groups\n", "    fgs = ifg.identify_functional_groups(mol)\n", "    for fg in fgs:\n", "        highlights += fg.atomIds\n", "    for hl in highlights:\n", "        colors[hl] = (0,0,1)\n", "    \n", "    atoms = [a.GetIdx() for a in mol.GetAtoms()]\n", "    atoms_left = [idx for idx in atoms if idx not in highlights]\n", "    \n", "    # Find all connected components with union-find algorithm\n", "    parent = {}\n", "    for a in atoms_left:\n", "        parent[a] = -1\n", "    for bond in mol.GetBonds():\n", "        beg = bond.GetBeginAtomIdx()\n", "        end = bond.GetEndAtomIdx()\n", "        if beg in parent and end in parent:\n", "            parent = union(parent, beg, end)\n", "    \n", "    starts = set(atoms_left)\n", "    for k, v in parent.items():\n", "        if v in starts:\n", "            starts.remove(v)\n", "    \n", "    components = []\n", "    for s in starts:\n", "        com = [s]\n", "        while parent[s] != -1:\n", "            s = parent[s]\n", "            com.append(s)\n", "        components.append(com)\n"]}, {"cell_type": "code", "execution_count": 85, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["263\n", "Counter({'c': 489127, '1': 243290, 'C': 122344, 'n': 50040, 'N': 22353, 'O': 6279, 's': 4030, '[': 3617, ']': 3617, 'H': 3380, 'o': 2759, '=': 2047, 'S': 918, '-': 583, '+': 226, 'B': 124, 'e': 9, 'i': 5, 'P': 4, '2': 3, '#': 1})\n", "dict_keys(['c1ccccc1', 'c1ccncc1', 'c1ccsc1', 'c1cscn1', 'c1cocn1', 'c1cnoc1', 'c1cnccn1', 'c1cnc1', 'c1ccnc1', 'c1cncnc1', 'c1cnnc1', 'c1cccc1', 'c1cncn1', 'c1ccocc1', 'c1coco1', 'c1cnnn1', 'c1csccnc1', 'c1coccn1', 'c1ncccnc1', 'c1cc1', 'c1ccoc1', 'c1ccncncnccccc1', 'c1cnncn1', 'c1ccnnc1', 'c1ncnn1', 'c1cnccnc1', 'c1coccc1', 'c1cnccc1', 'c1cscccn1', 'c1cocccn1', 'c1ncon1', 'c1ccc1', 'c1cncc1', 'c1cnncc1', 'c1ncnc1', 'c1cccncc1', 'c1nnnn1', 'c1cscco1', 'c1cocc1', 'c1ccocn1', 'b1ccco1', 'c1nnco1', 'c1cncccc1', 'c1csccs1', 'c1coc1', 'c1cscccc1', 'c1cococ1', 'c1csccn1', 'c1nncs1', 'b1occo1', 'c1coccnc1', 'c1co1', 'c1nccnc1', 'c1nocn1', 'c1nccs1', 'c1ccnco1', 'c1nccco1', 'c1cncccn1', 'c1cocco1', 'c1csnc1', 'c1ccnccc1', 'c1ncsn1', 'c1cccccc1', 'c1ccncn1', 'c1nccscc1', 'c1ncccn1', 'c1cnon1', 'c1nccc1', 'c1nccoc1', 'c1nncc1', 'c1ncncn1', 'c1cnsn1', 'c1cccnc1', 'c1nccn1', 'c1ccccccccccc1', 'c1ccscc1', 'c1nccncc1', 'c1coccoc1', 'c1ccncccc1', 'c1ccoco1', 'c1cccco1', 'c1cscs1', 'c1csenc1', 'c1cccn1', 'c1ncccc1', 'c1nnccc1', 'c1cscc1', 'c1cnccoc1', 'c1nnnc1', 'c1cocnc1', 'c1cncoc1', 'c1cnsc1', 'c1nocc1', 'c1ccoccc1', 'c1cccscc1', 'c1ccsn1', 'c1coo1', 'c1cnccsc1', 'c1nncco1', 'c1nncnc1', 'c1csco1', 'c1ccccccc1', 'c1nccccnc1', 'c1cccoc1', 'c1cocccnc1', 'c1csnn1', 'c1ccnncc1', 'c1cccocc1', 'c1ncco1', 'c1csccc1', 'c1cnco1', 'c1nsccn1', 'c1cncncc1', 'c1ccco1', 'c1cnscc1', 'c1ccccs1', 'c1ccnccncccccccnc1', 'c1cncco1', 'c1cncsc1', 'c1ccscn1', 'c1ccsec1', 'c1ccccn1', 'c1cococcc1', 'c1nccnn1', 'c1nnccn1', 'c1ccnccnc1', 'c1csncc1', 'c1csncn1', 'c1cccnccccnccccc1', 'c1nccccc1', 'c1cocccccocccncnccccc1', 'c1cnccccc1', 'c1ccsnc1', 'c1ncccs1', 'c1nnscc1', 'c1cncccnc1', 'c1coscc1', 'c1cncnn1', 'c1nccsn1', 'c1ccccccccccnccn1', 'c1cncncn1', 'c1cocccc1', 'c1coccco1', 'c1copoc1', 'c1ccoccccccnccncc1', 'c1ccccocccnccc1', 'c1ccccoccnccc1', 'c1ncnnn1', 'c1nncn1', 'c1ccccccccccccc1', 'c1ccnccsscccnc1', 'c1cconc1', 'b1occco1', 'c1ccsccc1', 'c1nncccc1', 'c1cccnccccncccc1', 'c1ccscnc1', 'c1cccnccc1', 'c1ncss1', 'c1ccncncncccccc1', 'c1ccnccn1', 'c1nscc1', 'c1ccncnc1', 'c1ccccnc1', 'c1ccsccncccccoc1', 'c1cccccccoccccccc1', 'c1nn1', 'c1ccnccs1', 'c1nncsc1', 'c1ccsicc1', 'c1cccnccnccccccc1', 'c1ccccoccc1', 'c1csccco1', 'c1cocccoc1', 'c1cn1', 'c1ccsecc1', 'c1scnn1', 'c1cnccncc1', 'c1cccoccccccc1', 'c1ccocccncnnccocccocc1', 'c1cocsn1', 'c1nscco1', 'c1ccoccn1', 'c1ccccncccc1', 'c1cnsnc1', 'c1ncscn1', 'c1ncncc1', 'c1cssc1', 'c1cncocc1', 'c1ccccocccccnccn1', 'c1ncocc1', 'c1ccococ1', 'c1cncccncccncccnc1', 'c1csncco1', 'c1cccccnccnccccc1', 'c1nccocc1', 'c1csecn1', 'c1cnnnc1', 'c1ncsc1', 'c1nsncc1', 'c1cpccn1', 'c1cosioc1', 'c1nocco1', 'c1cnccoccc1', 'c1nsnc1', 'c1ncccccc1', 'c1ccccco1', 'c1coccoccoccoccoccoccn1', 'c1ncnccn1', 'c1ccn1', 'c1cscsc1', 'c1cccsccc1', 'c1csc1', 'c1ncocn1', 'c1cnn1', 'c1cccsccnccn1', 'c1conc1', 'c1cosiosioc1', 'c1ccccccccccccccc1', 'c1noccc1', 'c1scnc1', 'c1cpccc1', 'c1cconccc1', 'c1csnccc1', 'c1ccccnccocccc1', 'c1nscccc1', 'c1ccnscc1', 'c1ccccccccc1', 'c1ccccncc1', 'c1ccocccncccncccc1', 'c1cscnc1', 'c1csnnc1', 'c1nnccs1', 'c1nccccn1', 'c1coccoccoccoccoccn1', 'c1ccccsccccccoccc1', 'c1csscc1', 'c1cccccccocccccc1', 'c1ccccccocccnccocccccc1', 'c1ccncccccnccnsnc1', 'c1ccccccocnccnccocc1', 'c1cnccnccc1', 'c1ncno1', 'c1cccnccoccccccoccncc1', 'c1coccncco1', 'c1ncscc1', 'c1cccccocnccnccoccc1', 'c1cosn1', 'c1cscccs1', 'c1csns1', 'c1cccccccccnccn1', 'c1cccccnccnccoccccccc1', 'c1ccccocccoccc1', 'c1cccoco1', 'c1ncs1', 'c1cccccccccc1', 'c1cnnccn1', 'c1cccccccccccccc1', 'c1cccccccncnccn1', 'c1coccnccncoccccccc1', 'c1csnccn1', 'c1ccccccccccccccccc1', 'c1cnccccccccccs1'])\n"]}], "source": ["print(len(ring_counter))\n", "print(token)\n", "print(ring_counter.keys())\n"]}, {"cell_type": "code", "execution_count": 86, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Counter({'c1ccccc1': 59364, 'c1ccncc1': 17488, 'c1ccnc1': 6540, 'c1cncnc1': 4724, 'c1cnccn1': 3562, 'c1cncn1': 3541, 'c1cnnc1': 3273, 'c1ccsc1': 2025, 'c1cc1': 1951, 'c1cscn1': 1932, 'c1cccc1': 1844, 'c1coccn1': 1593, 'c1ccoc1': 1351, 'c1ccocc1': 1009, 'c1cocn1': 799, 'c1ccnnc1': 753, 'c1cncc1': 700, 'c1ncnn1': 667, 'c1cnoc1': 626, 'c1cnc1': 535, 'c1coco1': 487, 'c1cnccc1': 439, 'c1ccc1': 423, 'c1cocc1': 350, 'c1nnnn1': 309, 'c1cnnn1': 302, 'c1cccncc1': 291, 'c1ncon1': 284, 'c1coccc1': 253, 'c1cnccnc1': 241, 'c1cccccc1': 190, 'c1cocco1': 140, 'c1nnco1': 132, 'c1csccn1': 127, 'c1co1': 125, 'c1ncncn1': 117, 'c1cnncn1': 103, 'c1cncccc1': 100, 'c1cococ1': 99, 'c1nncs1': 95, 'c1nccnc1': 94, 'c1cncccn1': 94, 'c1cnsc1': 92, 'b1occo1': 87, 'c1nccncc1': 85, 'c1cccnc1': 84, 'c1coccnc1': 82, 'c1coc1': 78, 'c1ccnccc1': 76, 'c1nccn1': 72, 'c1ncsn1': 68, 'c1ncccn1': 66, 'c1ncccc1': 66, 'c1nocc1': 64, 'c1cnsn1': 54, 'c1cncoc1': 54, 'c1ccscc1': 53, 'c1nccc1': 47, 'c1cnccoc1': 44, 'c1nnccc1': 43, 'c1nccs1': 41, 'c1cccocc1': 41, 'c1nncc1': 38, 'c1cccn1': 38, 'b1ccco1': 35, 'c1csnc1': 33, 'c1ncnc1': 32, 'c1csccc1': 31, 'c1ncco1': 29, 'c1cnon1': 28, 'c1ccoccc1': 28, 'c1cccoc1': 28, 'c1cscccn1': 27, 'c1csccnc1': 25, 'c1nsccn1': 25, 'c1cccco1': 24, 'c1cocccn1': 23, 'c1ccocn1': 22, 'c1ccccccc1': 21, 'c1cncncc1': 21, 'c1ccco1': 21, 'c1cnncc1': 20, 'c1nccoc1': 20, 'c1cncsc1': 20, 'c1ccncncnccccc1': 19, 'c1ncccnc1': 18, 'c1ccnco1': 18, 'c1cnccsc1': 18, 'c1nncn1': 18, 'c1cncco1': 16, 'c1ccccn1': 16, 'c1nccscc1': 15, 'c1ccscn1': 15, 'c1cscccc1': 14, 'c1nccco1': 14, 'c1cscc1': 13, 'c1cnscc1': 13, 'c1cscco1': 12, 'c1cccscc1': 12, 'c1csco1': 12, 'c1nccccc1': 12, 'c1cscs1': 11, 'c1nocn1': 10, 'c1csnn1': 10, 'c1ccnncc1': 10, 'c1cnco1': 10, 'c1cncnn1': 10, 'c1nscc1': 10, 'c1cn1': 10, 'c1ccccccccccnccn1': 9, 'c1ccccoccnccc1': 9, 'c1cocccccocccncnccccc1': 8, 'c1cocccc1': 8, 'c1cnn1': 8, 'c1cocnc1': 7, 'c1ccccnc1': 7, 'c1cocccnc1': 6, 'c1ccsnc1': 6, 'c1nncccc1': 6, 'c1ccscnc1': 6, 'c1ccccccccccc1': 5, 'c1ccsn1': 5, 'c1ccsec1': 5, 'c1coccco1': 5, 'c1cconc1': 5, 'c1cccnccc1': 5, 'c1cocsn1': 5, 'c1cscsc1': 5, 'c1ccncccc1': 4, 'c1ccnccnc1': 4, 'c1csncc1': 4, 'c1csncn1': 4, 'c1nnscc1': 4, 'c1cccnccnccccccc1': 4, 'c1cccoccccccc1': 4, 'c1ccoccn1': 4, 'c1ncncc1': 4, 'c1ccncn1': 3, 'c1nncnc1': 3, 'c1nccccnc1': 3, 'c1ccccs1': 3, 'c1nnccn1': 3, 'c1ncccs1': 3, 'c1cncccnc1': 3, 'c1ccnccs1': 3, 'c1nncsc1': 3, 'c1ccccoccc1': 3, 'c1ccccncccc1': 3, 'c1cncccncccncccnc1': 3, 'c1cnnnc1': 3, 'c1nsncc1': 3, 'c1csc1': 3, 'c1ccccccocccnccocccccc1': 3, 'c1coccoc1': 2, 'c1nnnc1': 2, 'c1nncco1': 2, 'c1ccnccncccccccnc1': 2, 'c1cococcc1': 2, 'c1cnccccc1': 2, 'c1nccsn1': 2, 'c1copoc1': 2, 'c1ncnnn1': 2, 'c1ccccccccccccc1': 2, 'b1occco1': 2, 'c1ccsccc1': 2, 'c1cccnccccncccc1': 2, 'c1ncss1': 2, 'c1ccnccn1': 2, 'c1ccncnc1': 2, 'c1ccsccncccccoc1': 2, 'c1ccsicc1': 2, 'c1scnn1': 2, 'c1ccocccncnnccocccocc1': 2, 'c1nscco1': 2, 'c1cnsnc1': 2, 'c1ncscn1': 2, 'c1cssc1': 2, 'c1cncocc1': 2, 'c1cccccnccnccccc1': 2, 'c1csecn1': 2, 'c1ncsc1': 2, 'c1ncnccn1': 2, 'c1csnccc1': 2, 'c1nscccc1': 2, 'c1ccccccccc1': 2, 'c1ccccncc1': 2, 'c1ccncccccnccnsnc1': 2, 'c1cnccnccc1': 2, 'c1cccoco1': 2, 'c1ncs1': 2, 'c1csccs1': 1, 'c1ccoco1': 1, 'c1csenc1': 1, 'c1coo1': 1, 'c1nccnn1': 1, 'c1cccnccccnccccc1': 1, 'c1coscc1': 1, 'c1cncncn1': 1, 'c1ccoccccccnccncc1': 1, 'c1ccccocccnccc1': 1, 'c1ccnccsscccnc1': 1, 'c1ccncncncccccc1': 1, 'c1cccccccoccccccc1': 1, 'c1nn1': 1, 'c1csccco1': 1, 'c1cocccoc1': 1, 'c1ccsecc1': 1, 'c1cnccncc1': 1, 'c1ccccocccccnccn1': 1, 'c1ncocc1': 1, 'c1ccococ1': 1, 'c1csncco1': 1, 'c1nccocc1': 1, 'c1cpccn1': 1, 'c1cosioc1': 1, 'c1nocco1': 1, 'c1cnccoccc1': 1, 'c1nsnc1': 1, 'c1ncccccc1': 1, 'c1ccccco1': 1, 'c1coccoccoccoccoccoccn1': 1, 'c1ccn1': 1, 'c1cccsccc1': 1, 'c1ncocn1': 1, 'c1cccsccnccn1': 1, 'c1conc1': 1, 'c1cosiosioc1': 1, 'c1ccccccccccccccc1': 1, 'c1noccc1': 1, 'c1scnc1': 1, 'c1cpccc1': 1, 'c1cconccc1': 1, 'c1ccccnccocccc1': 1, 'c1ccnscc1': 1, 'c1ccocccncccncccc1': 1, 'c1cscnc1': 1, 'c1csnnc1': 1, 'c1nnccs1': 1, 'c1nccccn1': 1, 'c1coccoccoccoccoccn1': 1, 'c1ccccsccccccoccc1': 1, 'c1csscc1': 1, 'c1cccccccocccccc1': 1, 'c1ccccccocnccnccocc1': 1, 'c1ncno1': 1, 'c1cccnccoccccccoccncc1': 1, 'c1coccncco1': 1, 'c1ncscc1': 1, 'c1cccccocnccnccoccc1': 1, 'c1cosn1': 1, 'c1cscccs1': 1, 'c1csns1': 1, 'c1cccccccccnccn1': 1, 'c1cccccnccnccoccccccc1': 1, 'c1ccccocccoccc1': 1, 'c1cccccccccc1': 1, 'c1cnnccn1': 1, 'c1cccccccccccccc1': 1, 'c1cccccccncnccn1': 1, 'c1coccnccncoccccccc1': 1, 'c1csnccn1': 1, 'c1ccccccccccccccccc1': 1, 'c1cnccccccccccs1': 1})\n"]}], "source": ["print(ring_counter)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.9"}}, "nbformat": 4, "nbformat_minor": 2}